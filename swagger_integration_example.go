package main

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/swagger"
	_ "your-project/docs" // 导入生成的docs包
)

// @title 服务器监控系统 API
// @version 1.0
// @description 这是一个高性能的服务器监控系统API，支持实时数据推送和WebSocket通信
// @description 
// @description ## 功能特性
// @description - 实时服务器状态监控
// @description - WebSocket实时数据推送  
// @description - 服务管理和控制
// @description - 系统日志查看
// @description - JWT认证授权
// @description
// @description ## 认证方式
// @description 使用Bearer Token进行认证，在请求头中添加：
// @description ```
// @description Authorization: Bearer <your-token>
// @description ```
//
// @termsOfService http://swagger.io/terms/
// @contact.name API Support Team
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>
// @license.name MIT License
// @license.url https://opensource.org/licenses/MIT
//
// @host localhost:7788
// @BasePath /api/v1
// @schemes http https
//
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
//
// @tag.name 认证
// @tag.description 用户认证和授权相关接口
// @tag.name 服务器
// @tag.description 服务器监控和管理相关接口
// @tag.name 服务
// @tag.description 系统服务管理相关接口
// @tag.name WebSocket
// @tag.description WebSocket实时通信接口
func main() {
	app := fiber.New()

	// Swagger路由
	app.Get("/swagger/*", swagger.HandlerDefault)

	// API路由
	setupAPIRoutes(app)

	app.Listen(":7788")
}

func setupAPIRoutes(app *fiber.App) {
	api := app.Group("/api/v1")

	// 认证相关
	auth := api.Group("/auth")
	auth.Post("/login", Login)
	auth.Post("/refresh", RefreshToken)

	// 服务器相关
	servers := api.Group("/servers")
	servers.Get("/", GetServers)
	servers.Get("/:id", GetServerByID)
	servers.Get("/:id/stats", GetServerStats)
	servers.Get("/:id/history", GetServerHistory)

	// 服务管理相关
	services := api.Group("/services")
	services.Get("/", GetServices)
	services.Post("/control", ControlService)
	services.Get("/logs", GetServiceLogs)
}

// 数据模型定义

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required" example:"admin" description:"用户名"`
	Password string `json:"password" validate:"required" example:"password123" description:"密码"`
} // @name LoginRequest

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." description:"JWT访问令牌"`
	ExpiresIn int    `json:"expires_in" example:"3600" description:"令牌过期时间（秒）"`
	User      User   `json:"user" description:"用户信息"`
} // @name LoginResponse

// User 用户信息
type User struct {
	ID       int    `json:"id" example:"1" description:"用户ID"`
	Username string `json:"username" example:"admin" description:"用户名"`
	Role     string `json:"role" example:"admin" description:"用户角色"`
} // @name User

// ServerInfo 服务器信息
type ServerInfo struct {
	ID          int     `json:"id" example:"1" description:"服务器ID"`
	Name        string  `json:"name" example:"Web-Server-01" description:"服务器名称"`
	IP          string  `json:"ip" example:"*************" description:"服务器IP地址"`
	Hostname    string  `json:"hostname" example:"web-server-01" description:"主机名"`
	OS          string  `json:"os" example:"Ubuntu 20.04" description:"操作系统"`
	Status      string  `json:"status" example:"online" enums:"online,offline,warning,critical" description:"服务器状态"`
	CPU         float64 `json:"cpu" example:"45.2" description:"CPU使用率（百分比）"`
	Memory      float64 `json:"memory" example:"68.5" description:"内存使用率（百分比）"`
	NetInSpeed  uint64  `json:"net_in_speed" example:"1048576" description:"网络入站速度（字节/秒）"`
	NetOutSpeed uint64  `json:"net_out_speed" example:"524288" description:"网络出站速度（字节/秒）"`
	Uptime      string  `json:"uptime" example:"15 days, 3:45:22" description:"运行时间"`
	LastSeen    string  `json:"last_seen" example:"2023-12-01T10:30:00Z" description:"最后在线时间"`
} // @name ServerInfo

// ServerStats 服务器详细统计信息
type ServerStats struct {
	CPU       CPUStats     `json:"cpu" description:"CPU统计信息"`
	Memory    MemoryStats  `json:"memory" description:"内存统计信息"`
	Disk      DiskStats    `json:"disk" description:"磁盘统计信息"`
	Network   NetworkStats `json:"network" description:"网络统计信息"`
	Processes int          `json:"processes" example:"156" description:"进程数量"`
	LoadAvg   []float64    `json:"load_avg" example:"[0.5, 0.8, 1.2]" description:"系统负载平均值"`
	Timestamp time.Time    `json:"timestamp" example:"2023-12-01T10:30:00Z" description:"统计时间"`
} // @name ServerStats

// CPUStats CPU统计信息
type CPUStats struct {
	Usage    float64 `json:"usage" example:"45.2" description:"CPU使用率（百分比）"`
	Cores    int     `json:"cores" example:"8" description:"CPU核心数"`
	Model    string  `json:"model" example:"Intel Core i7-9700K" description:"CPU型号"`
	Frequency float64 `json:"frequency" example:"3600.0" description:"CPU频率（MHz）"`
} // @name CPUStats

// MemoryStats 内存统计信息
type MemoryStats struct {
	Total     uint64  `json:"total" example:"16777216000" description:"总内存（字节）"`
	Used      uint64  `json:"used" example:"11486617600" description:"已用内存（字节）"`
	Available uint64  `json:"available" example:"5290598400" description:"可用内存（字节）"`
	Usage     float64 `json:"usage" example:"68.5" description:"内存使用率（百分比）"`
	SwapTotal uint64  `json:"swap_total" example:"2147483648" description:"交换分区总大小（字节）"`
	SwapUsed  uint64  `json:"swap_used" example:"0" description:"交换分区已用大小（字节）"`
} // @name MemoryStats

// DiskStats 磁盘统计信息
type DiskStats struct {
	Total uint64  `json:"total" example:"1000000000000" description:"磁盘总容量（字节）"`
	Used  uint64  `json:"used" example:"500000000000" description:"磁盘已用容量（字节）"`
	Free  uint64  `json:"free" example:"500000000000" description:"磁盘可用容量（字节）"`
	Usage float64 `json:"usage" example:"50.0" description:"磁盘使用率（百分比）"`
} // @name DiskStats

// NetworkStats 网络统计信息
type NetworkStats struct {
	InSpeed    uint64 `json:"in_speed" example:"1048576" description:"入站速度（字节/秒）"`
	OutSpeed   uint64 `json:"out_speed" example:"524288" description:"出站速度（字节/秒）"`
	InTotal    uint64 `json:"in_total" example:"1073741824000" description:"总入站流量（字节）"`
	OutTotal   uint64 `json:"out_total" example:"536870912000" description:"总出站流量（字节）"`
	PacketsIn  uint64 `json:"packets_in" example:"1000000" description:"入站数据包数"`
	PacketsOut uint64 `json:"packets_out" example:"800000" description:"出站数据包数"`
} // @name NetworkStats

// ServiceInfo 服务信息
type ServiceInfo struct {
	Name        string `json:"name" example:"nginx" description:"服务名称"`
	Type        string `json:"type" example:"systemd" enums:"systemd,supervisor,docker" description:"服务类型"`
	Status      string `json:"status" example:"running" enums:"running,stopped,failed,unknown" description:"服务状态"`
	Description string `json:"description" example:"Nginx HTTP Server" description:"服务描述"`
	PID         string `json:"pid" example:"1234" description:"进程ID"`
	CPU         string `json:"cpu" example:"2.5%" description:"CPU使用率"`
	Memory      string `json:"memory" example:"128MB" description:"内存使用量"`
	Uptime      int64  `json:"uptime" example:"86400" description:"运行时间（秒）"`
} // @name ServiceInfo

// ServiceControlRequest 服务控制请求
type ServiceControlRequest struct {
	ServerID    int    `json:"server_id" validate:"required" example:"1" description:"服务器ID"`
	ServiceName string `json:"service_name" validate:"required" example:"nginx" description:"服务名称"`
	ServiceType string `json:"service_type" validate:"required" example:"systemd" enums:"systemd,supervisor,docker" description:"服务类型"`
	Action      string `json:"action" validate:"required" example:"restart" enums:"start,stop,restart" description:"操作类型"`
} // @name ServiceControlRequest

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error" example:"Invalid request" description:"错误信息"`
	Code    int    `json:"code" example:"400" description:"错误代码"`
	Details string `json:"details,omitempty" example:"Missing required field: username" description:"详细错误信息"`
} // @name ErrorResponse

// SuccessResponse 成功响应
type SuccessResponse struct {
	Message string      `json:"message" example:"Operation completed successfully" description:"成功信息"`
	Data    interface{} `json:"data,omitempty" description:"响应数据"`
} // @name SuccessResponse

// API处理函数

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取JWT访问令牌，用于后续API调用的身份验证
// @Tags 认证
// @Accept json
// @Produce json
// @Param credentials body LoginRequest true "登录凭据"
// @Success 200 {object} LoginResponse "登录成功，返回访问令牌"
// @Failure 400 {object} ErrorResponse "请求格式错误"
// @Failure 401 {object} ErrorResponse "用户名或密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /auth/login [post]
func Login(c *fiber.Ctx) error {
	// 实现登录逻辑
	return c.JSON(LoginResponse{})
}

// RefreshToken 刷新访问令牌
// @Summary 刷新访问令牌
// @Description 使用有效的JWT令牌获取新的访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} LoginResponse "令牌刷新成功"
// @Failure 401 {object} ErrorResponse "令牌无效或已过期"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /auth/refresh [post]
func RefreshToken(c *fiber.Ctx) error {
	return c.JSON(LoginResponse{})
}

// GetServers 获取服务器列表
// @Summary 获取服务器列表
// @Description 获取所有监控服务器的基本信息和状态
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param status query string false "按状态过滤" Enums(online, offline, warning, critical)
// @Param limit query int false "返回数量限制" default(50) minimum(1) maximum(100)
// @Param offset query int false "偏移量" default(0) minimum(0)
// @Success 200 {array} ServerInfo "服务器列表"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /servers [get]
func GetServers(c *fiber.Ctx) error {
	return c.JSON([]ServerInfo{})
}

// GetServerByID 获取指定服务器信息
// @Summary 获取指定服务器信息
// @Description 根据服务器ID获取详细的服务器信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "服务器ID" minimum(1)
// @Success 200 {object} ServerInfo "服务器信息"
// @Failure 400 {object} ErrorResponse "无效的服务器ID"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 404 {object} ErrorResponse "服务器不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /servers/{id} [get]
func GetServerByID(c *fiber.Ctx) error {
	return c.JSON(ServerInfo{})
}

// GetServerStats 获取服务器统计信息
// @Summary 获取服务器统计信息
// @Description 获取指定服务器的详细统计信息，包括CPU、内存、磁盘、网络等
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "服务器ID" minimum(1)
// @Success 200 {object} ServerStats "服务器统计信息"
// @Failure 400 {object} ErrorResponse "无效的服务器ID"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 404 {object} ErrorResponse "服务器不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /servers/{id}/stats [get]
func GetServerStats(c *fiber.Ctx) error {
	return c.JSON(ServerStats{})
}

// GetServerHistory 获取服务器历史数据
// @Summary 获取服务器历史数据
// @Description 获取指定服务器的历史监控数据
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "服务器ID" minimum(1)
// @Param start query string false "开始时间" format(date-time) example(2023-12-01T00:00:00Z)
// @Param end query string false "结束时间" format(date-time) example(2023-12-01T23:59:59Z)
// @Param interval query string false "数据间隔" Enums(1m, 5m, 15m, 1h, 1d) default(5m)
// @Success 200 {array} ServerStats "历史统计数据"
// @Failure 400 {object} ErrorResponse "无效的参数"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 404 {object} ErrorResponse "服务器不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /servers/{id}/history [get]
func GetServerHistory(c *fiber.Ctx) error {
	return c.JSON([]ServerStats{})
}

// GetServices 获取服务列表
// @Summary 获取服务列表
// @Description 获取指定服务器的所有服务信息
// @Tags 服务
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param server_id query int true "服务器ID" minimum(1)
// @Param service_type query string false "服务类型过滤" Enums(systemd, supervisor, docker)
// @Param status query string false "状态过滤" Enums(running, stopped, failed, unknown)
// @Success 200 {array} ServiceInfo "服务列表"
// @Failure 400 {object} ErrorResponse "无效的参数"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /services [get]
func GetServices(c *fiber.Ctx) error {
	return c.JSON([]ServiceInfo{})
}

// ControlService 控制服务
// @Summary 控制服务
// @Description 对指定服务器的服务执行启动、停止或重启操作
// @Tags 服务
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body ServiceControlRequest true "服务控制请求"
// @Success 200 {object} SuccessResponse "操作成功"
// @Failure 400 {object} ErrorResponse "无效的请求参数"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 404 {object} ErrorResponse "服务不存在"
// @Failure 500 {object} ErrorResponse "操作失败"
// @Router /services/control [post]
func ControlService(c *fiber.Ctx) error {
	return c.JSON(SuccessResponse{Message: "Service controlled successfully"})
}

// GetServiceLogs 获取服务日志
// @Summary 获取服务日志
// @Description 获取指定服务的日志信息
// @Tags 服务
// @Accept json
// @Produce plain
// @Security BearerAuth
// @Param server_id query int true "服务器ID" minimum(1)
// @Param service_name query string true "服务名称"
// @Param service_type query string true "服务类型" Enums(systemd, supervisor, docker)
// @Param lines query int false "日志行数" default(100) minimum(1) maximum(1000)
// @Success 200 {string} string "服务日志内容"
// @Failure 400 {object} ErrorResponse "无效的参数"
// @Failure 401 {object} ErrorResponse "未授权访问"
// @Failure 404 {object} ErrorResponse "服务不存在"
// @Failure 500 {object} ErrorResponse "获取日志失败"
// @Router /services/logs [get]
func GetServiceLogs(c *fiber.Ctx) error {
	return c.SendString("Service logs content...")
}
