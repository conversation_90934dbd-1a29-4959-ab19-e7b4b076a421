package websocket

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/gofiber/websocket/v2"
)

// WebSocket消息类型常量
const (
	// 客户端发送的消息类型
	WSMsgTypeAuth      = "auth"
	WSMsgTypeSubscribe = "subscribe"
	WSMsgTypePing      = "ping"
	WSMsgTypePong      = "pong"
	WSMsgTypeData      = "data"

	// 服务器发送的消息类型
	WSMsgTypeAuthResponse         = "auth_response"
	WSMsgTypeSystemStatsBroadcast = "system_stats_broadcast"
	WSMsgTypeError                = "error"
)

// WSMessage WebSocket消息基础结构
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data,omitempty"`
	ServerID  int         `json:"server_id,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// ClientConnection 客户端连接信息
type ClientConnection struct {
	Conn     *websocket.Conn
	ServerID int
	LastSeen time.Time
	mutex    sync.RWMutex
}

// FrontendConnection 前端连接信息
type FrontendConnection struct {
	Conn         *websocket.Conn
	UserID       string
	SubscribedTo map[int]bool // 订阅的服务器ID
	LastPing     time.Time
	mutex        sync.RWMutex
}

// ConnectionManager WebSocket连接管理器
type ConnectionManager struct {
	clients   map[int]*ClientConnection       // 客户端连接 (key: serverID)
	frontends map[string]*FrontendConnection // 前端连接 (key: connectionID)
	mutex     sync.RWMutex
}

// NewConnectionManager 创建新的连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		clients:   make(map[int]*ClientConnection),
		frontends: make(map[string]*FrontendConnection),
	}
}

// AddClientConnection 添加客户端连接
func (cm *ConnectionManager) AddClientConnection(serverID int, conn *websocket.Conn) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if existing, exists := cm.clients[serverID]; exists {
		// 关闭旧连接
		existing.Conn.Close()
		log.Printf("替换服务器 %d 的现有连接", serverID)
	}

	cm.clients[serverID] = &ClientConnection{
		Conn:     conn,
		ServerID: serverID,
		LastSeen: time.Now(),
	}

	log.Printf("客户端连接已添加: 服务器ID %d", serverID)
}

// RemoveClientConnection 移除客户端连接
func (cm *ConnectionManager) RemoveClientConnection(serverID int) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.clients[serverID]; exists {
		conn.Conn.Close()
		delete(cm.clients, serverID)
		log.Printf("客户端连接已移除: 服务器ID %d", serverID)
	}
}

// AddFrontendConnection 添加前端连接
func (cm *ConnectionManager) AddFrontendConnection(connID string, conn *websocket.Conn) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.frontends[connID] = &FrontendConnection{
		Conn:         conn,
		UserID:       connID,
		SubscribedTo: make(map[int]bool),
		LastPing:     time.Now(),
	}

	log.Printf("前端连接已添加: %s", connID)
}

// RemoveFrontendConnection 移除前端连接
func (cm *ConnectionManager) RemoveFrontendConnection(connID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.frontends[connID]; exists {
		conn.Conn.Close()
		delete(cm.frontends, connID)
		log.Printf("前端连接已移除: %s", connID)
	}
}

// SubscribeToServer 前端订阅服务器
func (cm *ConnectionManager) SubscribeToServer(connID string, serverID int) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.frontends[connID]; exists {
		conn.mutex.Lock()
		conn.SubscribedTo[serverID] = true
		conn.mutex.Unlock()
		log.Printf("前端 %s 订阅服务器 %d", connID, serverID)
	}
}

// UnsubscribeFromServer 前端取消订阅服务器
func (cm *ConnectionManager) UnsubscribeFromServer(connID string, serverID int) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.frontends[connID]; exists {
		conn.mutex.Lock()
		delete(conn.SubscribedTo, serverID)
		conn.mutex.Unlock()
		log.Printf("前端 %s 取消订阅服务器 %d", connID, serverID)
	}
}

// BroadcastToFrontends 向订阅了指定服务器的前端广播消息
func (cm *ConnectionManager) BroadcastToFrontends(serverID int, message WSMessage) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	message.Timestamp = time.Now()
	messageData, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化广播消息失败: %v", err)
		return
	}

	broadcastCount := 0
	for connID, conn := range cm.frontends {
		conn.mutex.RLock()
		isSubscribed := conn.SubscribedTo[serverID]
		conn.mutex.RUnlock()

		if isSubscribed {
			if err := conn.Conn.WriteMessage(websocket.TextMessage, messageData); err != nil {
				log.Printf("向前端 %s 发送消息失败: %v", connID, err)
				// 连接可能已断开，标记为需要清理
				go cm.RemoveFrontendConnection(connID)
			} else {
				broadcastCount++
			}
		}
	}

	if broadcastCount > 0 {
		log.Printf("向 %d 个前端连接广播服务器 %d 的消息", broadcastCount, serverID)
	}
}

// SendToFrontend 向指定前端发送消息
func (cm *ConnectionManager) SendToFrontend(connID string, message WSMessage) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	conn, exists := cm.frontends[connID]
	if !exists {
		return log.Printf("前端连接不存在: %s", connID)
	}

	message.Timestamp = time.Now()
	messageData, err := json.Marshal(message)
	if err != nil {
		return err
	}

	return conn.Conn.WriteMessage(websocket.TextMessage, messageData)
}

// GetClientConnections 获取所有客户端连接信息
func (cm *ConnectionManager) GetClientConnections() map[int]*ClientConnection {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	result := make(map[int]*ClientConnection)
	for k, v := range cm.clients {
		result[k] = v
	}
	return result
}

// GetFrontendConnections 获取所有前端连接信息
func (cm *ConnectionManager) GetFrontendConnections() map[string]*FrontendConnection {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	result := make(map[string]*FrontendConnection)
	for k, v := range cm.frontends {
		result[k] = v
	}
	return result
}

// UpdateClientLastSeen 更新客户端最后活跃时间
func (cm *ConnectionManager) UpdateClientLastSeen(serverID int) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.clients[serverID]; exists {
		conn.mutex.Lock()
		conn.LastSeen = time.Now()
		conn.mutex.Unlock()
	}
}

// UpdateFrontendLastPing 更新前端最后ping时间
func (cm *ConnectionManager) UpdateFrontendLastPing(connID string) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.frontends[connID]; exists {
		conn.mutex.Lock()
		conn.LastPing = time.Now()
		conn.mutex.Unlock()
	}
}

// CleanupStaleConnections 清理过期连接
func (cm *ConnectionManager) CleanupStaleConnections(timeout time.Duration) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()

	// 清理过期的客户端连接
	for serverID, conn := range cm.clients {
		conn.mutex.RLock()
		lastSeen := conn.LastSeen
		conn.mutex.RUnlock()

		if now.Sub(lastSeen) > timeout {
			conn.Conn.Close()
			delete(cm.clients, serverID)
			log.Printf("清理过期客户端连接: 服务器ID %d", serverID)
		}
	}

	// 清理过期的前端连接
	for connID, conn := range cm.frontends {
		conn.mutex.RLock()
		lastPing := conn.LastPing
		conn.mutex.RUnlock()

		if now.Sub(lastPing) > timeout {
			conn.Conn.Close()
			delete(cm.frontends, connID)
			log.Printf("清理过期前端连接: %s", connID)
		}
	}
}

// StartCleanupRoutine 启动定期清理协程
func (cm *ConnectionManager) StartCleanupRoutine(interval, timeout time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for range ticker.C {
			cm.CleanupStaleConnections(timeout)
		}
	}()
}

// GetStats 获取连接统计信息
func (cm *ConnectionManager) GetStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return map[string]interface{}{
		"client_connections":   len(cm.clients),
		"frontend_connections": len(cm.frontends),
		"total_connections":    len(cm.clients) + len(cm.frontends),
	}
}
