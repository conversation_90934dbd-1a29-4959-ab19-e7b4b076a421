package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"
	"github.com/gofiber/websocket/v2"
)

// @title 服务器监控系统 API
// @version 2.0
// @description 基于Fiber的高性能服务器监控系统，支持实时数据推送和WebSocket通信
// @description
// @description ## 功能特性
// @description - 实时服务器状态监控
// @description - WebSocket实时数据推送
// @description - 服务管理和控制
// @description - 系统日志查看
// @description - JWT认证授权
// @description
// @description ## 认证方式
// @description 使用httpOnly Cookie进行认证，在请求中自动携带认证信息
//
// @termsOfService http://swagger.io/terms/
// @contact.name API Support Team
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>
// @license.name MIT License
// @license.url https://opensource.org/licenses/MIT
//
// @host localhost:7788
// @BasePath /api
// @schemes http https
//
// @securityDefinitions.apikey CookieAuth
// @in header
// @name Cookie
// @description httpOnly Cookie认证

// =============================================================================
// 从原main.go迁移的核心数据结构和变量
// =============================================================================

// 网络速度跟踪器 (迁移自原main.go)
type NetworkSpeedTracker struct {
	LastNetIn  uint64
	LastNetOut uint64
	LastTime   time.Time
	mutex      sync.RWMutex
}

var networkTracker = &NetworkSpeedTracker{
	LastTime: time.Now(),
}

// 服务器配置结构 (迁移自原main.go)
type ServerConfig struct {
	Listen        string `json:"listen"`
	Port          string `json:"port"`
	LoginUsername string `json:"login_username"`
	LoginPassword string `json:"login_password"`
	Servers       []struct {
		ID      int    `json:"mid"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	SecureCookie bool   `json:"secure_cookie"`
	Password     string `json:"password"`
}

// 客户端配置结构 (迁移自原main.go)
type ClientConfig struct {
	MID       int    `json:"mid"`
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password   string `json:"password"`
	ServerInfo struct {
		Name         string `json:"name"`
		Tag          string `json:"tag"`
		IPv4         string `json:"ipv4"`
		IPv6         string `json:"ipv6"`
		AutoDetectIP bool   `json:"auto_detect_ip"`
	} `json:"server_info"`
}

// 前端连接管理器 (迁移自原main.go)
type FrontendConnection struct {
	Conn         *websocket.Conn
	UserID       string
	SubscribedTo map[int]bool // 此连接订阅的服务器ID
	LastPing     time.Time
}

type ConnectionManager struct {
	connections map[string]*FrontendConnection
	mutex       sync.RWMutex
}

// 全局变量 (迁移自原main.go)
var (
	isClient            = flag.Bool("c", false, "Run as client")
	isServer            = flag.Bool("s", false, "Run as server")
	configFile          = flag.String("f", "", "Configuration file path")
	ServerConfigGlobal  *ServerConfig
	frontendConnManager = &ConnectionManager{
		connections: make(map[string]*FrontendConnection),
	}
)

// 内存缓存用于存储最新的服务器状态 (迁移自原main.go)
var serverStatusCache = struct {
	sync.RWMutex
	data map[int]*SystemStats
}{
	data: make(map[int]*SystemStats),
}

// calculateNetworkSpeed 计算每秒字节数的网络速度 (迁移自原main.go)
func (nt *NetworkSpeedTracker) calculateNetworkSpeed(currentNetIn, currentNetOut uint64) (uint64, uint64) {
	nt.mutex.Lock()
	defer nt.mutex.Unlock()

	now := time.Now()
	timeDiff := now.Sub(nt.LastTime).Seconds()

	var inSpeed, outSpeed uint64

	// 仅在我们有先前数据和合理时间差的情况下计算速度
	isFirstRun := nt.LastNetIn == 0 && nt.LastNetOut == 0
	if timeDiff > 0 && timeDiff < 300 && !isFirstRun {
		// 计算每秒字节数
		if currentNetIn >= nt.LastNetIn {
			inSpeed = uint64(float64(currentNetIn-nt.LastNetIn) / timeDiff)
		}
		if currentNetOut >= nt.LastNetOut {
			outSpeed = uint64(float64(currentNetOut-nt.LastNetOut) / timeDiff)
		}
	}

	// 更新跟踪数据
	nt.LastNetIn = currentNetIn
	nt.LastNetOut = currentNetOut
	nt.LastTime = now

	return inSpeed, outSpeed
}

// loadServerConfig 加载服务器配置 (迁移自原main.go)
func loadServerConfig(filename string) (*ServerConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config ServerConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func main() {
	flag.Parse()

	if !*isClient && !*isServer {
		fmt.Println("Please specify either -c (client) or -s (server)")
		flag.Usage()
		os.Exit(1)
	}

	if *configFile == "" {
		if *isServer {
			*configFile = "../server.json"
		} else {
			*configFile = "../client.json"
		}
	}

	if *isServer {
		runServer()
	} else {
		runClient()
	}
}

// runServer 运行服务器模式 (迁移自原main.go)
func runServer() {
	fmt.Println("Starting Fiber server mode...")

	config, err := loadServerConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load server config: %v", err)
	}

	// 全局存储配置以供处理程序使用
	ServerConfigGlobal = config

	// 启动Fiber服务器
	startFiberServer(config)
}

// runClient 运行客户端模式占位符
func runClient() {
	fmt.Println("Client mode not implemented in Fiber version yet")
	os.Exit(1)
}

// startFiberServer 启动Fiber服务器
func startFiberServer(config *ServerConfig) {
	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		// 启用预分叉模式以提高性能（生产环境）
		Prefork: false,

		// 自定义错误处理
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			return c.Status(code).JSON(fiber.Map{
				"success":   false,
				"error":     err.Error(),
				"code":      code,
				"timestamp": time.Now(),
			})
		},

		// 设置读取超时
		ReadTimeout: time.Second * 10,

		// 设置写入超时
		WriteTimeout: time.Second * 10,

		// 设置最大请求体大小
		BodyLimit: 4 * 1024 * 1024, // 4MB
	})

	// 中间件配置
	setupMiddleware(app)

	// API路由配置
	setupRoutes(app)

	// WebSocket路由配置
	setupWebSocket(app)

	// Swagger文档
	app.Get("/swagger/*", swagger.HandlerDefault)

	// 启动实时数据广播 (迁移自原main.go)
	go startRealTimeDataBroadcast()

	// 启动服务器
	addr := fmt.Sprintf("%s:%s", config.Listen, config.Port)
	log.Printf("🚀 Fiber服务器启动在 %s", addr)
	log.Printf("📚 API文档: http://%s/swagger/", addr)
	log.Printf("🌐 应用地址: http://%s/", addr)
	log.Printf("🔌 WebSocket路径: %s", config.WebSocket.Path)

	log.Fatal(app.Listen(addr))
}

// startRealTimeDataBroadcast 启动实时数据广播 (占位符)
func startRealTimeDataBroadcast() {
	log.Println("实时数据广播已启动")
	// 这里应该实现实时数据广播逻辑
}

// setupMiddleware 配置中间件
func setupMiddleware(app *fiber.App) {
	// 恢复中间件
	app.Use(recover.New())

	// 日志中间件
	app.Use(logger.New(logger.Config{
		Format:     "[${time}] ${status} - ${method} ${path} (${latency})\n",
		TimeFormat: "2006-01-02 15:04:05",
	}))

	// CORS中间件
	app.Use(cors.New(cors.Config{
		AllowOrigins:     "*",
		AllowMethods:     "GET,POST,HEAD,PUT,DELETE,PATCH",
		AllowHeaders:     "Origin, Content-Type, Accept, Authorization, Cookie",
		AllowCredentials: true, // 允许携带Cookie
	}))

	// 静态文件服务
	app.Static("/", "../web")
}

// setupRoutes 配置API路由
func setupRoutes(app *fiber.App) {
	// API组
	api := app.Group("/api")

	// 健康检查
	api.Get("/health", healthCheck)

	// 认证路由
	auth := api.Group("/auth")
	auth.Post("/login", loginHandler)
	auth.Post("/logout", logoutHandler)

	// 服务器监控路由
	servers := api.Group("/servers")
	servers.Get("/", getServersHandler)
	servers.Get("/:id", getServerByIDHandler)

	// 系统统计路由
	system := api.Group("/system")
	system.Get("/stats", getSystemStatsHandler)

	// 服务管理路由
	services := api.Group("/services")
	services.Get("/list", getServicesListHandler)
	services.Post("/:type/:action", serviceActionHandler)
	services.Get("/logs", getServiceLogsHandler)
}

// setupWebSocket 配置WebSocket路由
func setupWebSocket(app *fiber.App) {
	// 启动WebSocket管理器
	startWebSocketManager()

	// 客户端WebSocket连接（用于监控数据上报）
	app.Get("/ws", websocket.New(handleClientWebSocket))

	// 前端WebSocket连接（用于实时数据推送）
	app.Get("/ws/frontend", websocket.New(handleFrontendWebSocket))
}

// =============================================================================
// API处理函数
// =============================================================================

// healthCheck 健康检查
// @Summary 健康检查
// @Description 检查服务器运行状态
// @Tags 系统
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "服务器运行正常"
// @Router /health [get]
func healthCheck(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"success":   true,
		"message":   "服务器运行正常",
		"timestamp": time.Now(),
		"version":   "2.0.0",
	})
}

// loginHandler 登录处理
// @Summary 用户登录
// @Description 用户登录获取认证Cookie
// @Tags 认证
// @Accept json
// @Produce json
// @Param credentials body LoginRequest true "登录凭据"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Router /auth/login [post]
func loginHandler(c *fiber.Ctx) error {
	var req LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(ErrorResponse{
			Error: "无效的请求格式",
			Code:  400,
		})
	}

	// 验证用户凭据（这里使用硬编码，实际应该从数据库验证）
	if req.Username == "xctcc" && req.Password == "960423Wc@" {
		// 设置httpOnly Cookie
		c.Cookie(&fiber.Cookie{
			Name:     "auth_token",
			Value:    "authenticated_user_token", // 实际应该是JWT或session ID
			Expires:  time.Now().Add(24 * time.Hour),
			HTTPOnly: true,
			Secure:   false, // 开发环境设为false，生产环境应为true
			SameSite: "Lax",
		})

		return c.JSON(LoginResponse{
			Success: true,
			Message: "登录成功",
			User: UserInfo{
				ID:       1,
				Username: req.Username,
				Role:     "admin",
			},
		})
	}

	return c.Status(401).JSON(ErrorResponse{
		Error: "用户名或密码错误",
		Code:  401,
	})
}

// logoutHandler 登出处理
// @Summary 用户登出
// @Description 清除认证Cookie
// @Tags 认证
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "登出成功"
// @Router /auth/logout [post]
func logoutHandler(c *fiber.Ctx) error {
	// 清除Cookie
	c.Cookie(&fiber.Cookie{
		Name:     "auth_token",
		Value:    "",
		Expires:  time.Now().Add(-time.Hour),
		HTTPOnly: true,
	})

	return c.JSON(fiber.Map{
		"success": true,
		"message": "登出成功",
	})
}

// getServersHandler 获取服务器列表
// @Summary 获取服务器列表
// @Description 获取所有监控服务器的状态信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security CookieAuth
// @Success 200 {array} ServerInfo "服务器列表"
// @Failure 401 {object} ErrorResponse "未授权"
// @Router /servers [get]
func getServersHandler(c *fiber.Ctx) error {
	// 检查认证
	if !isAuthenticated(c) {
		return c.Status(401).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  401,
		})
	}

	// 返回模拟数据（实际应该从数据库获取）
	servers := make([]ServerInfo, 12)
	for i := 0; i < 12; i++ {
		servers[i] = ServerInfo{
			ID:            i + 1,
			Name:          fmt.Sprintf("服务器 %d", i+1),
			IP:            "N/A",
			Hostname:      "N/A",
			OS:            "N/A",
			Status:        "offline",
			CPU:           0,
			Memory:        0,
			NetInSpeed:    0,
			NetOutSpeed:   0,
			Uptime:        "--",
			LastActive:    0,
			IsPlaceholder: true,
		}
	}

	return c.JSON(servers)
}

// getServerByIDHandler 获取指定服务器信息
// @Summary 获取指定服务器信息
// @Description 根据服务器ID获取详细信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security CookieAuth
// @Param id path int true "服务器ID"
// @Success 200 {object} ServerInfo "服务器信息"
// @Failure 404 {object} ErrorResponse "服务器不存在"
// @Router /servers/{id} [get]
func getServerByIDHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(401).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  401,
		})
	}

	serverID := c.Params("id")
	// 这里应该根据ID从数据库获取服务器信息
	// 暂时返回模拟数据

	return c.JSON(ServerInfo{
		ID:       1,
		Name:     "服务器 " + serverID,
		IP:       "*************",
		Hostname: "server-" + serverID,
		OS:       "Ubuntu 20.04",
		Status:   "online",
		CPU:      45.2,
		Memory:   68.5,
		Uptime:   "15 days, 3:45:22",
	})
}

// getSystemStatsHandler 获取系统统计信息
// @Summary 获取系统统计信息
// @Description 获取指定服务器的系统统计信息
// @Tags 系统
// @Accept json
// @Produce json
// @Security CookieAuth
// @Param serverId query int true "服务器ID"
// @Success 200 {object} SystemStats "系统统计信息"
// @Router /system/stats [get]
func getSystemStatsHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(401).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  401,
		})
	}

	// 返回模拟的系统统计数据
	stats := SystemStats{
		CPU:            45.2,
		MemUsed:        8589934592,  // 8GB
		MemTotal:       17179869184, // 16GB
		Memory:         50.0,
		DiskUsed:       536870912000,  // 500GB
		DiskTotal:      1073741824000, // 1TB
		Disk:           50.0,
		NetInSpeed:     1048576,       // 1MB/s
		NetOutSpeed:    524288,        // 512KB/s
		NetInTransfer:  1073741824000, // 1TB
		NetOutTransfer: 536870912000,  // 500GB
		Uptime:         1296000,       // 15 days
	}

	return c.JSON(stats)
}

// 其他处理函数的占位符
func getServicesListHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(401).JSON(ErrorResponse{Error: "未授权访问", Code: 401})
	}
	return c.JSON([]ServiceInfo{})
}

func serviceActionHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(401).JSON(ErrorResponse{Error: "未授权访问", Code: 401})
	}
	return c.JSON(ServiceResponse{Success: true, Message: "操作成功"})
}

func getServiceLogsHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(401).JSON(ErrorResponse{Error: "未授权访问", Code: 401})
	}
	return c.SendString("Service logs content...")
}

// =============================================================================
// WebSocket处理函数
// =============================================================================

func handleClientWebSocket(c *websocket.Conn) {
	defer c.Close()
	log.Println("客户端WebSocket连接建立")

	for {
		var msg map[string]interface{}
		if err := c.ReadJSON(&msg); err != nil {
			log.Printf("客户端连接读取错误: %v", err)
			break
		}

		log.Printf("收到客户端消息: %+v", msg)
		// 处理客户端消息逻辑
	}
}

func handleFrontendWebSocket(c *websocket.Conn) {
	defer c.Close()
	log.Println("前端WebSocket连接建立")

	for {
		var msg WSMessage
		if err := c.ReadJSON(&msg); err != nil {
			log.Printf("前端连接读取错误: %v", err)
			break
		}

		log.Printf("收到前端消息: %+v", msg)

		// 处理不同类型的消息
		switch msg.Type {
		case WSMsgTypeAuth:
			// 处理认证
			response := WSMessage{
				Type: WSMsgTypeAuthResponse,
				Data: map[string]interface{}{
					"success": true,
					"user":    "authenticated_user",
				},
				Timestamp: time.Now(),
			}
			c.WriteJSON(response)

		case WSMsgTypeSubscribe:
			// 处理订阅
			log.Printf("前端订阅服务器: %d", msg.ServerID)

		case WSMsgTypePing:
			// 处理心跳
			response := WSMessage{
				Type:      WSMsgTypePong,
				Timestamp: time.Now(),
			}
			c.WriteJSON(response)
		}
	}
}

// =============================================================================
// 辅助函数
// =============================================================================

// isAuthenticated 检查用户是否已认证
func isAuthenticated(c *fiber.Ctx) bool {
	// 检查Cookie中的认证信息
	authToken := c.Cookies("auth_token")
	return authToken == "authenticated_user_token"
}
