package config

import (
	"os"
	"strconv"
)

// Config 应用配置结构
type Config struct {
	// 服务器配置
	Port    string `json:"port"`
	Host    string `json:"host"`
	Prefork bool   `json:"prefork"`

	// 数据库配置
	DatabaseURL string `json:"database_url"`

	// 认证配置
	JWTSecret    string `json:"jwt_secret"`
	SecureCookie bool   `json:"secure_cookie"`

	// WebSocket配置
	WebSocketEnabled bool   `json:"websocket_enabled"`
	WebSocketPath    string `json:"websocket_path"`

	// 日志配置
	LogLevel string `json:"log_level"`

	// 环境配置
	Environment string `json:"environment"`

	// 监控配置
	MonitoringEnabled bool `json:"monitoring_enabled"`
	MetricsPath       string `json:"metrics_path"`

	// 安全配置
	RateLimitEnabled bool `json:"rate_limit_enabled"`
	RateLimitRPS     int  `json:"rate_limit_rps"`

	// 文件上传配置
	MaxFileSize int64 `json:"max_file_size"`

	// 超时配置
	ReadTimeout  int `json:"read_timeout"`
	WriteTimeout int `json:"write_timeout"`
}

// Load 加载配置
func Load() *Config {
	return &Config{
		Port:              getEnv("PORT", "7788"),
		Host:              getEnv("HOST", "0.0.0.0"),
		Prefork:           getEnvBool("PREFORK", false),
		DatabaseURL:       getEnv("DATABASE_URL", "./monitor.db"),
		JWTSecret:         getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		SecureCookie:      getEnvBool("SECURE_COOKIE", false),
		WebSocketEnabled:  getEnvBool("WEBSOCKET_ENABLED", true),
		WebSocketPath:     getEnv("WEBSOCKET_PATH", "/ws"),
		LogLevel:          getEnv("LOG_LEVEL", "info"),
		Environment:       getEnv("ENVIRONMENT", "development"),
		MonitoringEnabled: getEnvBool("MONITORING_ENABLED", true),
		MetricsPath:       getEnv("METRICS_PATH", "/metrics"),
		RateLimitEnabled:  getEnvBool("RATE_LIMIT_ENABLED", false),
		RateLimitRPS:      getEnvInt("RATE_LIMIT_RPS", 100),
		MaxFileSize:       getEnvInt64("MAX_FILE_SIZE", 4*1024*1024), // 4MB
		ReadTimeout:       getEnvInt("READ_TIMEOUT", 10),
		WriteTimeout:      getEnvInt("WRITE_TIMEOUT", 10),
	}
}

// IsDevelopment 检查是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction 检查是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// GetListenAddress 获取监听地址
func (c *Config) GetListenAddress() string {
	return c.Host + ":" + c.Port
}

// 辅助函数

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBool 获取布尔类型环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getEnvInt 获取整数类型环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getEnvInt64 获取int64类型环境变量
func getEnvInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseInt(value, 10, 64); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 这里可以添加配置验证逻辑
	// 例如检查必需的配置项是否存在
	return nil
}

// GetDatabaseConfig 获取数据库配置
func (c *Config) GetDatabaseConfig() map[string]interface{} {
	return map[string]interface{}{
		"url":             c.DatabaseURL,
		"max_connections": getEnvInt("DB_MAX_CONNECTIONS", 25),
		"max_idle":        getEnvInt("DB_MAX_IDLE", 25),
		"max_lifetime":    getEnvInt("DB_MAX_LIFETIME", 300), // 5 minutes
	}
}

// GetWebSocketConfig 获取WebSocket配置
func (c *Config) GetWebSocketConfig() map[string]interface{} {
	return map[string]interface{}{
		"enabled":              c.WebSocketEnabled,
		"path":                 c.WebSocketPath,
		"max_connections":      getEnvInt("WS_MAX_CONNECTIONS", 1000),
		"read_buffer_size":     getEnvInt("WS_READ_BUFFER_SIZE", 4096),
		"write_buffer_size":    getEnvInt("WS_WRITE_BUFFER_SIZE", 4096),
		"ping_interval":        getEnvInt("WS_PING_INTERVAL", 30), // seconds
		"pong_timeout":         getEnvInt("WS_PONG_TIMEOUT", 10),  // seconds
		"max_message_size":     getEnvInt64("WS_MAX_MESSAGE_SIZE", 1024*1024), // 1MB
		"compression_enabled":  getEnvBool("WS_COMPRESSION_ENABLED", true),
	}
}

// GetSecurityConfig 获取安全配置
func (c *Config) GetSecurityConfig() map[string]interface{} {
	return map[string]interface{}{
		"secure_cookie":      c.SecureCookie,
		"jwt_secret":         c.JWTSecret,
		"rate_limit_enabled": c.RateLimitEnabled,
		"rate_limit_rps":     c.RateLimitRPS,
		"cors_enabled":       getEnvBool("CORS_ENABLED", true),
		"cors_origins":       getEnv("CORS_ORIGINS", "*"),
		"csrf_enabled":       getEnvBool("CSRF_ENABLED", false),
		"helmet_enabled":     getEnvBool("HELMET_ENABLED", true),
	}
}

// GetMonitoringConfig 获取监控配置
func (c *Config) GetMonitoringConfig() map[string]interface{} {
	return map[string]interface{}{
		"enabled":      c.MonitoringEnabled,
		"metrics_path": c.MetricsPath,
		"health_path":  getEnv("HEALTH_PATH", "/health"),
		"pprof_enabled": getEnvBool("PPROF_ENABLED", c.IsDevelopment()),
		"pprof_path":   getEnv("PPROF_PATH", "/debug/pprof"),
	}
}

// GetLoggingConfig 获取日志配置
func (c *Config) GetLoggingConfig() map[string]interface{} {
	return map[string]interface{}{
		"level":       c.LogLevel,
		"format":      getEnv("LOG_FORMAT", "json"),
		"output":      getEnv("LOG_OUTPUT", "stdout"),
		"file_path":   getEnv("LOG_FILE_PATH", "./logs/app.log"),
		"max_size":    getEnvInt("LOG_MAX_SIZE", 100), // MB
		"max_backups": getEnvInt("LOG_MAX_BACKUPS", 3),
		"max_age":     getEnvInt("LOG_MAX_AGE", 28), // days
		"compress":    getEnvBool("LOG_COMPRESS", true),
	}
}
