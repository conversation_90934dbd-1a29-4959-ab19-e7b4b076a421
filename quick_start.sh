#!/bin/bash

# 服务器监控系统 Fiber 迁移快速开始脚本
# 作者: AI Assistant
# 版本: 1.0

set -e

echo "🚀 开始 Fiber 框架迁移..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Go环境
check_go() {
    print_info "检查Go环境..."
    if ! command -v go &> /dev/null; then
        print_error "Go未安装，请先安装Go 1.21+"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    print_success "Go版本: $GO_VERSION"
}

# 备份原文件
backup_original() {
    print_info "备份原始文件..."
    
    if [ -f "main.go" ]; then
        cp main.go main.go.backup
        print_success "已备份 main.go -> main.go.backup"
    fi
    
    if [ -f "go.mod" ]; then
        cp go.mod go.mod.backup
        print_success "已备份 go.mod -> go.mod.backup"
    fi
}

# 安装依赖
install_dependencies() {
    print_info "安装Fiber及相关依赖..."
    
    # 核心依赖
    go get github.com/gofiber/fiber/v2
    go get github.com/gofiber/websocket/v2
    go get github.com/gofiber/swagger
    
    # 中间件
    go get github.com/gofiber/fiber/v2/middleware/cors
    go get github.com/gofiber/fiber/v2/middleware/logger
    go get github.com/gofiber/fiber/v2/middleware/recover
    go get github.com/gofiber/fiber/v2/middleware/jwt
    
    # Swagger工具
    go get github.com/swaggo/swag
    
    # 开发工具
    print_info "安装开发工具..."
    go install github.com/swaggo/swag/cmd/swag@latest
    go install github.com/air-verse/air@latest
    
    print_success "依赖安装完成"
}

# 创建项目结构
create_project_structure() {
    print_info "创建项目结构..."
    
    # 创建目录
    mkdir -p cmd/server
    mkdir -p internal/{config,handlers,middleware,models,services,websocket}
    mkdir -p pkg/{auth,utils,validator}
    mkdir -p docs
    mkdir -p configs
    mkdir -p scripts
    
    print_success "项目结构创建完成"
}

# 生成基础配置文件
generate_config_files() {
    print_info "生成配置文件..."
    
    # 生成 .env 文件
    cat > .env << 'EOF'
# 服务器配置
PORT=7788
PREFORK=false

# 数据库配置
DATABASE_URL=./monitor.db

# JWT配置
JWT_SECRET=your-secret-key-change-in-production

# 日志配置
LOG_LEVEL=info

# 开发模式
ENV=development
EOF

    # 生成 .air.toml 文件
    cat > .air.toml << 'EOF'
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = ["-s"]
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main cmd/server/main.go"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "build", "docs"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
EOF

    print_success "配置文件生成完成"
}

# 生成基础代码文件
generate_base_code() {
    print_info "生成基础代码文件..."
    
    # 生成主程序
    cat > cmd/server/main.go << 'EOF'
package main

import (
    "log"
    "os"
    
    "github.com/gofiber/fiber/v2"
    "github.com/gofiber/fiber/v2/middleware/cors"
    "github.com/gofiber/fiber/v2/middleware/logger"
    "github.com/gofiber/fiber/v2/middleware/recover"
    "github.com/gofiber/swagger"
)

// @title 服务器监控系统 API
// @version 2.0
// @description 基于Fiber的高性能服务器监控系统
// @host localhost:7788
// @BasePath /api/v1
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
func main() {
    // 创建Fiber应用
    app := fiber.New(fiber.Config{
        ErrorHandler: func(c *fiber.Ctx, err error) error {
            code := fiber.StatusInternalServerError
            if e, ok := err.(*fiber.Error); ok {
                code = e.Code
            }
            return c.Status(code).JSON(fiber.Map{
                "error": err.Error(),
                "code":  code,
            })
        },
    })
    
    // 中间件
    app.Use(recover.New())
    app.Use(logger.New())
    app.Use(cors.New(cors.Config{
        AllowOrigins: "*",
        AllowMethods: "GET,POST,HEAD,PUT,DELETE,PATCH",
        AllowHeaders: "Origin, Content-Type, Accept, Authorization",
    }))
    
    // 静态文件
    app.Static("/", "./web")
    
    // API路由
    api := app.Group("/api/v1")
    
    // 健康检查
    api.Get("/health", func(c *fiber.Ctx) error {
        return c.JSON(fiber.Map{
            "status": "ok",
            "message": "Server is running",
        })
    })
    
    // Swagger文档
    app.Get("/swagger/*", swagger.HandlerDefault)
    
    // 获取端口
    port := os.Getenv("PORT")
    if port == "" {
        port = "7788"
    }
    
    log.Printf("🚀 服务器启动在端口 %s", port)
    log.Printf("📚 API文档: http://localhost:%s/swagger/", port)
    
    log.Fatal(app.Listen(":" + port))
}
EOF

    # 生成配置文件
    cat > internal/config/config.go << 'EOF'
package config

import (
    "os"
)

type Config struct {
    Port        string
    Prefork     bool
    DatabaseURL string
    JWTSecret   string
    LogLevel    string
    Environment string
}

func Load() *Config {
    return &Config{
        Port:        getEnv("PORT", "7788"),
        Prefork:     getEnv("PREFORK", "false") == "true",
        DatabaseURL: getEnv("DATABASE_URL", "./monitor.db"),
        JWTSecret:   getEnv("JWT_SECRET", "your-secret-key"),
        LogLevel:    getEnv("LOG_LEVEL", "info"),
        Environment: getEnv("ENV", "development"),
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}
EOF

    print_success "基础代码文件生成完成"
}

# 生成测试文件
generate_test_files() {
    print_info "生成测试文件..."
    
    cat > cmd/server/main_test.go << 'EOF'
package main

import (
    "net/http/httptest"
    "testing"
    
    "github.com/gofiber/fiber/v2"
    "github.com/stretchr/testify/assert"
)

func TestHealthCheck(t *testing.T) {
    app := fiber.New()
    
    app.Get("/api/v1/health", func(c *fiber.Ctx) error {
        return c.JSON(fiber.Map{
            "status": "ok",
            "message": "Server is running",
        })
    })
    
    req := httptest.NewRequest("GET", "/api/v1/health", nil)
    resp, _ := app.Test(req)
    
    assert.Equal(t, 200, resp.StatusCode)
}
EOF

    print_success "测试文件生成完成"
}

# 更新go.mod
update_go_mod() {
    print_info "更新go.mod..."
    
    if [ ! -f "go.mod" ]; then
        go mod init server-monitor
    fi
    
    go mod tidy
    print_success "go.mod更新完成"
}

# 生成Swagger文档
generate_swagger() {
    print_info "生成Swagger文档..."
    
    if command -v swag &> /dev/null; then
        swag init -g cmd/server/main.go -o docs/
        print_success "Swagger文档生成完成"
        print_info "文档访问地址: http://localhost:7788/swagger/"
    else
        print_warning "swag命令未找到，请手动运行: swag init -g cmd/server/main.go -o docs/"
    fi
}

# 创建启动脚本
create_scripts() {
    print_info "创建启动脚本..."
    
    # 开发模式启动脚本
    cat > scripts/dev.sh << 'EOF'
#!/bin/bash
echo "🔥 启动开发模式（热重载）..."
air -c .air.toml
EOF

    # 生产模式启动脚本
    cat > scripts/build.sh << 'EOF'
#!/bin/bash
echo "🏗️  构建生产版本..."
swag init -g cmd/server/main.go -o docs/
go build -o build/server-monitor cmd/server/main.go
echo "✅ 构建完成: build/server-monitor"
EOF

    # 测试脚本
    cat > scripts/test.sh << 'EOF'
#!/bin/bash
echo "🧪 运行测试..."
go test -v ./...
echo "📊 生成覆盖率报告..."
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
echo "✅ 测试完成，覆盖率报告: coverage.html"
EOF

    chmod +x scripts/*.sh
    print_success "启动脚本创建完成"
}

# 显示下一步操作
show_next_steps() {
    echo ""
    print_success "🎉 Fiber迁移环境准备完成！"
    echo ""
    print_info "下一步操作："
    echo "1. 启动开发服务器:"
    echo "   ./scripts/dev.sh"
    echo ""
    echo "2. 或者直接运行:"
    echo "   go run cmd/server/main.go"
    echo ""
    echo "3. 访问应用:"
    echo "   - 应用地址: http://localhost:7788"
    echo "   - API文档: http://localhost:7788/swagger/"
    echo "   - 健康检查: http://localhost:7788/api/v1/health"
    echo ""
    echo "4. 开始迁移你的代码:"
    echo "   - 参考 FIBER_MIGRATION_PLAN.md"
    echo "   - 查看 fiber_refactor_example.go"
    echo "   - 使用 Makefile 进行构建"
    echo ""
    print_warning "注意: 原始文件已备份为 .backup 后缀"
}

# 主函数
main() {
    echo "🔧 Fiber框架迁移快速开始"
    echo "================================"
    
    check_go
    backup_original
    install_dependencies
    create_project_structure
    generate_config_files
    generate_base_code
    generate_test_files
    update_go_mod
    generate_swagger
    create_scripts
    show_next_steps
}

# 运行主函数
main "$@"
