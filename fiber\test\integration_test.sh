#!/bin/bash

# 服务器监控系统 Fiber 版本集成测试脚本
# 用于验证迁移后的功能是否正常工作

set -e

echo "🚀 开始 Fiber 版本集成测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 服务器配置
SERVER_HOST="localhost"
SERVER_PORT="7788"
BASE_URL="http://${SERVER_HOST}:${SERVER_PORT}"
WS_URL="ws://${SERVER_HOST}:${SERVER_PORT}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试函数
test_endpoint() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local expected_status="$4"
    local data="$5"
    local headers="$6"
    
    ((TOTAL_TESTS++))
    log_info "测试: $name"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -d '$data' -H 'Content-Type: application/json'"
    fi
    
    curl_cmd="$curl_cmd $BASE_URL$endpoint"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        log_success "$name - 状态码: $status_code"
        echo "  响应: $body" | head -c 100
        echo
    else
        log_error "$name - 期望状态码: $expected_status, 实际: $status_code"
        echo "  响应: $body"
    fi
}

# 检查服务器是否运行
check_server() {
    log_info "检查服务器是否运行在 $BASE_URL"
    
    if curl -s --connect-timeout 5 "$BASE_URL/api/health" > /dev/null; then
        log_success "服务器运行正常"
        return 0
    else
        log_error "服务器未运行或无法连接"
        log_info "请先启动服务器: cd fiber && make dev"
        return 1
    fi
}

# 测试健康检查
test_health_check() {
    log_info "=== 测试健康检查 ==="
    test_endpoint "健康检查" "GET" "/api/health" "200"
}

# 测试认证功能
test_authentication() {
    log_info "=== 测试认证功能 ==="
    
    # 测试登录成功
    test_endpoint "登录成功" "POST" "/api/auth/login" "200" \
        '{"username":"xctcc","password":"960423Wc@"}'
    
    # 测试登录失败
    test_endpoint "登录失败" "POST" "/api/auth/login" "401" \
        '{"username":"wrong","password":"wrong"}'
    
    # 测试登出
    test_endpoint "登出" "POST" "/api/auth/logout" "200"
}

# 测试服务器API
test_servers_api() {
    log_info "=== 测试服务器API ==="
    
    # 测试未认证访问
    test_endpoint "未认证获取服务器列表" "GET" "/api/servers" "401"
    
    # 测试已认证访问
    test_endpoint "已认证获取服务器列表" "GET" "/api/servers" "200" "" \
        "-H 'Cookie: auth_token=authenticated_user_token'"
    
    # 测试获取指定服务器
    test_endpoint "获取指定服务器" "GET" "/api/servers/1" "200" "" \
        "-H 'Cookie: auth_token=authenticated_user_token'"
}

# 测试系统统计API
test_system_stats() {
    log_info "=== 测试系统统计API ==="
    
    test_endpoint "获取系统统计" "GET" "/api/system/stats?serverId=1" "200" "" \
        "-H 'Cookie: auth_token=authenticated_user_token'"
}

# 测试服务管理API
test_services_api() {
    log_info "=== 测试服务管理API ==="
    
    # 测试获取服务列表
    test_endpoint "获取服务列表" "GET" "/api/services/list?serverId=1" "200" "" \
        "-H 'Cookie: auth_token=authenticated_user_token'"
    
    # 测试服务操作
    test_endpoint "服务启动操作" "POST" "/api/services/systemd/start" "200" \
        '{"serverId":1,"serviceName":"nginx","serviceType":"systemd"}' \
        "-H 'Cookie: auth_token=authenticated_user_token'"
    
    # 测试获取服务日志
    test_endpoint "获取服务日志" "GET" "/api/services/logs?serverId=1&serviceName=nginx&serviceType=systemd" "200" "" \
        "-H 'Cookie: auth_token=authenticated_user_token'"
}

# 测试WebSocket连接
test_websocket() {
    log_info "=== 测试WebSocket连接 ==="
    
    ((TOTAL_TESTS++))
    
    # 检查是否安装了websocat
    if ! command -v websocat &> /dev/null; then
        log_warning "websocat 未安装，跳过WebSocket测试"
        log_info "安装websocat: cargo install websocat 或 brew install websocat"
        return
    fi
    
    # 测试前端WebSocket连接
    log_info "测试前端WebSocket连接"
    
    # 创建测试消息
    local test_message='{"type":"auth","timestamp":"'$(date -Iseconds)'"}'
    
    # 使用websocat测试WebSocket连接
    if echo "$test_message" | timeout 5 websocat "$WS_URL/ws/frontend" > /dev/null 2>&1; then
        log_success "前端WebSocket连接测试"
    else
        log_error "前端WebSocket连接失败"
    fi
}

# 测试静态文件服务
test_static_files() {
    log_info "=== 测试静态文件服务 ==="
    
    # 测试根路径
    test_endpoint "静态文件根路径" "GET" "/" "200"
    
    # 测试不存在的文件
    test_endpoint "不存在的静态文件" "GET" "/nonexistent.html" "404"
}

# 性能测试
test_performance() {
    log_info "=== 性能测试 ==="
    
    ((TOTAL_TESTS++))
    
    log_info "执行健康检查性能测试 (100次请求)"
    
    local start_time=$(date +%s.%N)
    
    for i in {1..100}; do
        curl -s "$BASE_URL/api/health" > /dev/null
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    local rps=$(echo "scale=2; 100 / $duration" | bc)
    
    log_success "性能测试完成"
    echo "  总时间: ${duration}s"
    echo "  平均RPS: ${rps}"
    
    # 检查性能是否符合预期
    if (( $(echo "$rps > 100" | bc -l) )); then
        log_success "性能测试通过 (RPS > 100)"
    else
        log_warning "性能可能需要优化 (RPS: $rps)"
    fi
}

# 测试与Vue3前端的兼容性
test_vue3_compatibility() {
    log_info "=== 测试Vue3前端兼容性 ==="
    
    ((TOTAL_TESTS++))
    
    # 测试服务器列表格式
    log_info "验证服务器列表格式"
    local servers_response=$(curl -s -H 'Cookie: auth_token=authenticated_user_token' "$BASE_URL/api/servers")
    
    # 检查是否返回12个服务器
    local server_count=$(echo "$servers_response" | jq '. | length' 2>/dev/null || echo "0")
    
    if [ "$server_count" = "12" ]; then
        log_success "服务器列表格式正确 (12个服务器)"
    else
        log_error "服务器列表格式错误 (期望12个，实际$server_count个)"
    fi
    
    # 检查服务器对象结构
    local has_required_fields=$(echo "$servers_response" | jq '.[0] | has("id") and has("name") and has("status") and has("isPlaceholder")' 2>/dev/null || echo "false")
    
    if [ "$has_required_fields" = "true" ]; then
        log_success "服务器对象结构正确"
    else
        log_error "服务器对象缺少必需字段"
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    # 这里可以添加清理逻辑
}

# 主测试流程
main() {
    echo "🔍 Fiber版本服务器监控系统集成测试"
    echo "========================================"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，无法执行测试"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，某些测试可能无法执行"
    fi
    
    # 检查服务器
    if ! check_server; then
        exit 1
    fi
    
    # 执行测试
    test_health_check
    test_authentication
    test_servers_api
    test_system_stats
    test_services_api
    test_static_files
    test_websocket
    test_vue3_compatibility
    test_performance
    
    # 显示测试结果
    echo
    echo "========================================"
    echo "🏁 测试完成"
    echo "========================================"
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！Fiber版本迁移成功！${NC}"
        exit 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败${NC}"
        exit 1
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 运行主函数
main "$@"
