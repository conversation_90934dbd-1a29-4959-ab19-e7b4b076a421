# Fiber框架迁移详细计划

## 📋 迁移概览

**目标框架**: Fiber v2  
**预计时间**: 2-3周  
**风险等级**: 中等  
**预期收益**: 3-5倍性能提升

## 🎯 迁移目标

1. **性能提升**: HTTP处理能力提升3-5倍
2. **WebSocket优化**: 支持更多并发连接
3. **API文档**: 自动生成标准Swagger文档
4. **代码质量**: 更清晰的项目结构
5. **维护性**: 降低维护成本

## 📅 迁移时间表

### 第1周：准备和基础迁移

#### Day 1-2: 环境准备
- [ ] 安装Fiber及相关依赖
- [ ] 设置开发环境和工具
- [ ] 创建新的项目结构
- [ ] 配置Makefile和自动化脚本

#### Day 3-5: 核心框架搭建
- [ ] 创建Fiber应用主程序
- [ ] 配置中间件（CORS、日志、认证等）
- [ ] 实现基础路由结构
- [ ] 集成Swagger文档生成

### 第2周：功能迁移

#### Day 6-8: API路由迁移
- [ ] 迁移认证相关API
- [ ] 迁移服务器监控API
- [ ] 迁移服务管理API
- [ ] 添加Swagger注解

#### Day 9-10: WebSocket迁移
- [ ] 迁移客户端WebSocket连接
- [ ] 迁移前端WebSocket连接
- [ ] 实现连接管理器
- [ ] 测试WebSocket功能

### 第3周：测试和优化

#### Day 11-12: 测试验证
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能基准测试
- [ ] 压力测试

#### Day 13-15: 优化和部署
- [ ] 性能优化
- [ ] 文档完善
- [ ] 部署配置
- [ ] 监控集成

## 🏗️ 详细实施步骤

### 步骤1: 依赖安装和项目初始化

```bash
# 1. 安装核心依赖
go get github.com/gofiber/fiber/v2
go get github.com/gofiber/websocket/v2
go get github.com/gofiber/swagger
go get github.com/swaggo/swag/cmd/swag

# 2. 安装中间件
go get github.com/gofiber/fiber/v2/middleware/cors
go get github.com/gofiber/fiber/v2/middleware/logger
go get github.com/gofiber/fiber/v2/middleware/recover
go get github.com/gofiber/fiber/v2/middleware/jwt

# 3. 安装开发工具
go install github.com/swaggo/swag/cmd/swag@latest
go install github.com/air-verse/air@latest
```

### 步骤2: 项目结构重构

```
server-monitor/
├── cmd/
│   └── server/
│       └── main.go              # 应用入口
├── internal/
│   ├── config/                  # 配置管理
│   │   ├── config.go
│   │   └── database.go
│   ├── handlers/                # HTTP处理器
│   │   ├── auth.go
│   │   ├── server.go
│   │   └── service.go
│   ├── middleware/              # 中间件
│   │   ├── auth.go
│   │   ├── cors.go
│   │   └── logger.go
│   ├── models/                  # 数据模型
│   │   ├── server.go
│   │   ├── service.go
│   │   └── user.go
│   ├── services/                # 业务逻辑
│   │   ├── monitor.go
│   │   ├── database.go
│   │   └── websocket.go
│   └── websocket/               # WebSocket处理
│       ├── client.go
│       ├── frontend.go
│       └── manager.go
├── pkg/                         # 公共包
│   ├── auth/                    # 认证工具
│   ├── utils/                   # 工具函数
│   └── validator/               # 数据验证
├── docs/                        # Swagger文档
├── web/                         # 静态文件
├── configs/                     # 配置文件
├── scripts/                     # 脚本文件
├── Makefile                     # 构建脚本
├── Dockerfile                   # Docker配置
└── docker-compose.yml           # Docker Compose
```

### 步骤3: 核心代码迁移

#### 3.1 主程序 (cmd/server/main.go)

```go
package main

import (
    "log"
    "os"
    
    "github.com/gofiber/fiber/v2"
    "github.com/gofiber/swagger"
    
    "server-monitor/internal/config"
    "server-monitor/internal/handlers"
    "server-monitor/internal/middleware"
    "server-monitor/internal/services"
    _ "server-monitor/docs"
)

// @title 服务器监控系统 API
// @version 2.0
// @description 基于Fiber的高性能服务器监控系统
// @host localhost:7788
// @BasePath /api/v1
func main() {
    // 加载配置
    cfg := config.Load()
    
    // 初始化服务
    services.Initialize(cfg)
    
    // 创建Fiber应用
    app := fiber.New(fiber.Config{
        Prefork: cfg.Prefork,
        ErrorHandler: middleware.ErrorHandler,
    })
    
    // 设置中间件
    middleware.Setup(app)
    
    // 设置路由
    handlers.Setup(app)
    
    // Swagger文档
    app.Get("/swagger/*", swagger.HandlerDefault)
    
    // 启动服务器
    port := os.Getenv("PORT")
    if port == "" {
        port = "7788"
    }
    
    log.Fatal(app.Listen(":" + port))
}
```

#### 3.2 路由处理器迁移

**原代码模式**:
```go
func getServersAPI(w http.ResponseWriter, r *http.Request) {
    // 处理逻辑
    json.NewEncoder(w).Encode(servers)
}
```

**新代码模式**:
```go
// @Summary 获取服务器列表
// @Tags 服务器
// @Success 200 {array} models.ServerInfo
// @Router /servers [get]
func GetServers(c *fiber.Ctx) error {
    servers := services.GetAllServers()
    return c.JSON(servers)
}
```

#### 3.3 WebSocket迁移

**原代码**:
```go
func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
    conn, err := upgrader.Upgrade(w, r, nil)
    // ...
}
```

**新代码**:
```go
func HandleClientWebSocket(c *websocket.Conn) {
    defer c.Close()
    
    clientID := c.Query("id")
    manager.AddClient(clientID, c)
    defer manager.RemoveClient(clientID)
    
    for {
        var msg models.ClientMessage
        if err := c.ReadJSON(&msg); err != nil {
            break
        }
        
        services.ProcessClientMessage(&msg)
        manager.BroadcastToFrontends(&msg)
    }
}
```

### 步骤4: 配置管理

```go
// internal/config/config.go
type Config struct {
    Port        string `env:"PORT" envDefault:"7788"`
    Prefork     bool   `env:"PREFORK" envDefault:"false"`
    DatabaseURL string `env:"DATABASE_URL" envDefault:"./monitor.db"`
    JWTSecret   string `env:"JWT_SECRET" envDefault:"your-secret-key"`
    LogLevel    string `env:"LOG_LEVEL" envDefault:"info"`
}

func Load() *Config {
    cfg := &Config{}
    if err := env.Parse(cfg); err != nil {
        log.Fatal("Failed to parse config:", err)
    }
    return cfg
}
```

### 步骤5: 中间件配置

```go
// internal/middleware/setup.go
func Setup(app *fiber.App) {
    // 恢复中间件
    app.Use(recover.New())
    
    // 日志中间件
    app.Use(logger.New(logger.Config{
        Format: "[${time}] ${status} - ${method} ${path} (${latency})\n",
    }))
    
    // CORS中间件
    app.Use(cors.New(cors.Config{
        AllowOrigins: "*",
        AllowMethods: "GET,POST,HEAD,PUT,DELETE,PATCH",
        AllowHeaders: "Origin, Content-Type, Accept, Authorization",
    }))
    
    // 静态文件
    app.Static("/", "./web")
}
```

## 🧪 测试策略

### 1. 单元测试

```go
func TestGetServers(t *testing.T) {
    app := fiber.New()
    app.Get("/api/servers", handlers.GetServers)
    
    req := httptest.NewRequest("GET", "/api/servers", nil)
    resp, _ := app.Test(req)
    
    assert.Equal(t, 200, resp.StatusCode)
}
```

### 2. 集成测试

```bash
# 启动测试服务器
make run &

# 运行API测试
curl -X GET http://localhost:7788/api/v1/servers

# 测试WebSocket连接
wscat -c ws://localhost:7788/ws
```

### 3. 性能测试

```bash
# HTTP性能测试
make benchmark

# 压力测试
wrk -t12 -c400 -d30s http://localhost:7788/api/v1/servers

# WebSocket测试
go test -bench=BenchmarkWebSocket
```

## 🚀 部署策略

### 1. 开发环境

```bash
# 热重载开发
make dev

# 或使用air
air -c .air.toml
```

### 2. 生产环境

```bash
# 构建生产版本
make build

# Docker部署
make docker-build
make docker-run
```

### 3. 监控配置

```go
// 添加健康检查端点
app.Get("/health", func(c *fiber.Ctx) error {
    return c.JSON(fiber.Map{
        "status": "ok",
        "timestamp": time.Now(),
        "version": version,
    })
})

// 添加性能监控
app.Get("/metrics", monitor.New())
```

## ⚠️ 风险控制

### 1. 回滚计划

- 保留原main.go作为备份
- 使用Git分支管理
- 准备快速回滚脚本

### 2. 渐进式迁移

- 先迁移只读API
- 再迁移写入API
- 最后迁移WebSocket

### 3. 监控告警

- 设置性能监控
- 配置错误告警
- 监控内存使用

## 📊 成功指标

### 性能指标
- [ ] HTTP QPS提升3倍以上
- [ ] WebSocket并发连接数提升2倍
- [ ] 内存使用减少50%
- [ ] 响应时间减少60%

### 功能指标
- [ ] 所有API功能正常
- [ ] WebSocket实时通信正常
- [ ] Swagger文档完整
- [ ] 认证授权正常

### 质量指标
- [ ] 代码覆盖率>80%
- [ ] 无严重Bug
- [ ] 文档完整
- [ ] 部署成功

## 📚 学习资源

1. **Fiber官方文档**: https://docs.gofiber.io/
2. **Swagger集成指南**: https://github.com/swaggo/swag
3. **性能优化最佳实践**: https://github.com/gofiber/fiber/wiki
4. **WebSocket使用指南**: https://github.com/gofiber/websocket

## 🎉 迁移完成检查清单

- [ ] 所有依赖安装完成
- [ ] 项目结构重构完成
- [ ] API路由迁移完成
- [ ] WebSocket功能迁移完成
- [ ] Swagger文档生成正常
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试达标
- [ ] 部署配置完成
- [ ] 监控告警配置完成
- [ ] 文档更新完成
- [ ] 团队培训完成
