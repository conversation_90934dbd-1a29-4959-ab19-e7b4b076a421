package test

import (
	"encoding/json"
	"net/url"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"server-monitor-fiber/internal/websocket"
)

// TestWebSocketConnectionManager 测试WebSocket连接管理器
func TestWebSocketConnectionManager(t *testing.T) {
	manager := websocket.NewConnectionManager()

	t.Run("添加和移除客户端连接", func(t *testing.T) {
		// 模拟WebSocket连接
		mockConn := &MockWebSocketConn{}
		
		// 添加连接
		manager.AddClientConnection(1, mockConn)
		
		// 验证连接已添加
		connections := manager.GetClientConnections()
		assert.Len(t, connections, 1)
		assert.Contains(t, connections, 1)
		
		// 移除连接
		manager.RemoveClientConnection(1)
		
		// 验证连接已移除
		connections = manager.GetClientConnections()
		assert.Len(t, connections, 0)
	})

	t.Run("添加和移除前端连接", func(t *testing.T) {
		mockConn := &MockWebSocketConn{}
		connID := "frontend_test"
		
		// 添加连接
		manager.AddFrontendConnection(connID, mockConn)
		
		// 验证连接已添加
		connections := manager.GetFrontendConnections()
		assert.Len(t, connections, 1)
		assert.Contains(t, connections, connID)
		
		// 移除连接
		manager.RemoveFrontendConnection(connID)
		
		// 验证连接已移除
		connections = manager.GetFrontendConnections()
		assert.Len(t, connections, 0)
	})

	t.Run("前端订阅和取消订阅", func(t *testing.T) {
		mockConn := &MockWebSocketConn{}
		connID := "frontend_test"
		serverID := 1
		
		// 添加前端连接
		manager.AddFrontendConnection(connID, mockConn)
		
		// 订阅服务器
		manager.SubscribeToServer(connID, serverID)
		
		// 验证订阅
		connections := manager.GetFrontendConnections()
		conn := connections[connID]
		assert.True(t, conn.SubscribedTo[serverID])
		
		// 取消订阅
		manager.UnsubscribeFromServer(connID, serverID)
		
		// 验证取消订阅
		connections = manager.GetFrontendConnections()
		conn = connections[connID]
		assert.False(t, conn.SubscribedTo[serverID])
		
		// 清理
		manager.RemoveFrontendConnection(connID)
	})

	t.Run("广播消息到前端", func(t *testing.T) {
		mockConn := &MockWebSocketConn{}
		connID := "frontend_test"
		serverID := 1
		
		// 添加前端连接并订阅
		manager.AddFrontendConnection(connID, mockConn)
		manager.SubscribeToServer(connID, serverID)
		
		// 创建测试消息
		message := websocket.WSMessage{
			Type:     "system_stats_broadcast",
			ServerID: serverID,
			Data: map[string]interface{}{
				"CPU":    45.2,
				"Memory": 68.5,
			},
		}
		
		// 广播消息
		manager.BroadcastToFrontends(serverID, message)
		
		// 验证消息已发送
		assert.Len(t, mockConn.SentMessages, 1)
		
		var receivedMsg websocket.WSMessage
		err := json.Unmarshal(mockConn.SentMessages[0], &receivedMsg)
		assert.NoError(t, err)
		assert.Equal(t, "system_stats_broadcast", receivedMsg.Type)
		assert.Equal(t, serverID, receivedMsg.ServerID)
		
		// 清理
		manager.RemoveFrontendConnection(connID)
	})

	t.Run("连接统计", func(t *testing.T) {
		mockConn1 := &MockWebSocketConn{}
		mockConn2 := &MockWebSocketConn{}
		
		// 添加连接
		manager.AddClientConnection(1, mockConn1)
		manager.AddFrontendConnection("frontend_1", mockConn2)
		
		// 获取统计信息
		stats := manager.GetStats()
		assert.Equal(t, 1, stats["client_connections"])
		assert.Equal(t, 1, stats["frontend_connections"])
		assert.Equal(t, 2, stats["total_connections"])
		
		// 清理
		manager.RemoveClientConnection(1)
		manager.RemoveFrontendConnection("frontend_1")
	})
}

// TestWebSocketMessages 测试WebSocket消息处理
func TestWebSocketMessages(t *testing.T) {
	t.Run("系统统计消息", func(t *testing.T) {
		message := websocket.WSMessage{
			Type:     "system_stats",
			ServerID: 1,
			Data: map[string]interface{}{
				"CPU":    45.2,
				"Memory": 68.5,
				"Uptime": 86400,
			},
			Timestamp: time.Now(),
		}
		
		// 序列化和反序列化测试
		data, err := json.Marshal(message)
		assert.NoError(t, err)
		
		var decoded websocket.WSMessage
		err = json.Unmarshal(data, &decoded)
		assert.NoError(t, err)
		
		assert.Equal(t, message.Type, decoded.Type)
		assert.Equal(t, message.ServerID, decoded.ServerID)
	})

	t.Run("认证消息", func(t *testing.T) {
		message := websocket.WSMessage{
			Type: "auth",
			Data: map[string]interface{}{
				"token": "test_token",
			},
			Timestamp: time.Now(),
		}
		
		data, err := json.Marshal(message)
		assert.NoError(t, err)
		
		var decoded websocket.WSMessage
		err = json.Unmarshal(data, &decoded)
		assert.NoError(t, err)
		
		assert.Equal(t, "auth", decoded.Type)
		assert.NotNil(t, decoded.Data)
	})

	t.Run("订阅消息", func(t *testing.T) {
		message := websocket.WSMessage{
			Type:     "subscribe",
			ServerID: 1,
			Timestamp: time.Now(),
		}
		
		data, err := json.Marshal(message)
		assert.NoError(t, err)
		
		var decoded websocket.WSMessage
		err = json.Unmarshal(data, &decoded)
		assert.NoError(t, err)
		
		assert.Equal(t, "subscribe", decoded.Type)
		assert.Equal(t, 1, decoded.ServerID)
	})
}

// TestWebSocketCleanup 测试连接清理
func TestWebSocketCleanup(t *testing.T) {
	manager := websocket.NewConnectionManager()
	
	t.Run("清理过期连接", func(t *testing.T) {
		mockConn := &MockWebSocketConn{}
		
		// 添加连接
		manager.AddClientConnection(1, mockConn)
		manager.AddFrontendConnection("frontend_1", mockConn)
		
		// 验证连接已添加
		assert.Len(t, manager.GetClientConnections(), 1)
		assert.Len(t, manager.GetFrontendConnections(), 1)
		
		// 清理过期连接（使用很短的超时时间）
		manager.CleanupStaleConnections(1 * time.Nanosecond)
		
		// 验证连接已清理
		assert.Len(t, manager.GetClientConnections(), 0)
		assert.Len(t, manager.GetFrontendConnections(), 0)
	})
}

// MockWebSocketConn 模拟WebSocket连接
type MockWebSocketConn struct {
	SentMessages [][]byte
	Closed       bool
}

func (m *MockWebSocketConn) WriteMessage(messageType int, data []byte) error {
	m.SentMessages = append(m.SentMessages, data)
	return nil
}

func (m *MockWebSocketConn) ReadMessage() (messageType int, p []byte, err error) {
	// 模拟读取消息
	return websocket.TextMessage, []byte(`{"type":"ping"}`), nil
}

func (m *MockWebSocketConn) Close() error {
	m.Closed = true
	return nil
}

func (m *MockWebSocketConn) ReadJSON(v interface{}) error {
	// 模拟读取JSON消息
	testMsg := map[string]interface{}{
		"type": "ping",
	}
	data, _ := json.Marshal(testMsg)
	return json.Unmarshal(data, v)
}

func (m *MockWebSocketConn) WriteJSON(v interface{}) error {
	data, err := json.Marshal(v)
	if err != nil {
		return err
	}
	return m.WriteMessage(websocket.TextMessage, data)
}

func (m *MockWebSocketConn) SetReadDeadline(t time.Time) error {
	return nil
}

func (m *MockWebSocketConn) Query(key string) string {
	if key == "id" {
		return "1"
	}
	return ""
}

// TestWebSocketIntegration 集成测试
func TestWebSocketIntegration(t *testing.T) {
	// 这个测试需要实际的WebSocket服务器运行
	t.Skip("需要实际的WebSocket服务器进行集成测试")
	
	// 示例集成测试代码
	serverURL := "ws://localhost:7788/ws/frontend"
	u, err := url.Parse(serverURL)
	assert.NoError(t, err)
	
	// 连接到WebSocket服务器
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		t.Skipf("无法连接到WebSocket服务器: %v", err)
		return
	}
	defer conn.Close()
	
	// 发送认证消息
	authMsg := map[string]interface{}{
		"type": "auth",
		"data": map[string]string{
			"token": "test_token",
		},
	}
	
	err = conn.WriteJSON(authMsg)
	assert.NoError(t, err)
	
	// 读取响应
	var response map[string]interface{}
	err = conn.ReadJSON(&response)
	assert.NoError(t, err)
	assert.Equal(t, "auth_response", response["type"])
}

// BenchmarkWebSocketManager 性能测试
func BenchmarkWebSocketManager(b *testing.B) {
	manager := websocket.NewConnectionManager()
	
	b.Run("添加客户端连接", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			mockConn := &MockWebSocketConn{}
			manager.AddClientConnection(i, mockConn)
		}
	})
	
	b.Run("广播消息", func(b *testing.B) {
		// 预先添加一些连接
		for i := 0; i < 100; i++ {
			mockConn := &MockWebSocketConn{}
			connID := fmt.Sprintf("frontend_%d", i)
			manager.AddFrontendConnection(connID, mockConn)
			manager.SubscribeToServer(connID, 1)
		}
		
		message := websocket.WSMessage{
			Type:     "system_stats_broadcast",
			ServerID: 1,
			Data:     map[string]interface{}{"CPU": 45.2},
		}
		
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			manager.BroadcastToFrontends(1, message)
		}
	})
}
