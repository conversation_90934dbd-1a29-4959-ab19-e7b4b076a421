package main

import (
	"time"
)

// =============================================================================
// 统一数据结构设计 - 基于Vue3和Web前端需求
// =============================================================================

// WebSocket消息基础结构
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data,omitempty"`
	ServerID  int         `json:"server_id,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// 前端WebSocket消息类型
const (
	// 客户端发送的消息类型
	WSMsgTypeAuth      = "auth"
	WSMsgTypeSubscribe = "subscribe"
	WSMsgTypePing      = "ping"
	WSMsgTypePong      = "pong"

	// 服务器发送的消息类型
	WSMsgTypeAuthResponse       = "auth_response"
	WSMsgTypeSystemStatsBroadcast = "system_stats_broadcast"
	WSMsgTypeError              = "error"
)

// =============================================================================
// 服务器相关数据结构
// =============================================================================

// ServerInfo 服务器基础信息 (对应Vue3 servers.js)
type ServerInfo struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	IP          string  `json:"ip"`
	Hostname    string  `json:"hostname"`
	OS          string  `json:"os"`
	Status      string  `json:"status"` // online, offline, warning, critical
	CPU         float64 `json:"cpu"`
	Memory      float64 `json:"memory"`
	NetInSpeed  uint64  `json:"NetInSpeed"`
	NetOutSpeed uint64  `json:"NetOutSpeed"`
	NetInTransfer  uint64 `json:"NetInTransfer"`
	NetOutTransfer uint64 `json:"NetOutTransfer"`
	Uptime      string  `json:"uptime"`
	LastActive  int64   `json:"last_active"`
	IsPlaceholder bool  `json:"isPlaceholder,omitempty"`
}

// SystemStats 系统统计信息 (对应Vue3 services.js systemStats)
type SystemStats struct {
	CPU           float64 `json:"CPU"`
	MemUsed       uint64  `json:"MemUsed"`
	MemTotal      uint64  `json:"MemTotal"`
	Memory        float64 `json:"memory"`        // 计算得出的百分比
	DiskUsed      uint64  `json:"DiskUsed"`
	DiskTotal     uint64  `json:"DiskTotal"`
	Disk          float64 `json:"disk"`          // 计算得出的百分比
	NetInSpeed    uint64  `json:"NetInSpeed"`
	NetOutSpeed   uint64  `json:"NetOutSpeed"`
	NetInTransfer uint64  `json:"NetInTransfer"`
	NetOutTransfer uint64 `json:"NetOutTransfer"`
	Uptime        uint64  `json:"Uptime"`
	LoadAvg       []float64 `json:"LoadAvg,omitempty"`
	Processes     int     `json:"Processes,omitempty"`
}

// =============================================================================
// 服务管理相关数据结构
// =============================================================================

// ServiceInfo 服务信息 (对应Vue3 services.js)
type ServiceInfo struct {
	Name        string `json:"name"`
	Status      string `json:"status"` // running, stopped, failed, active, inactive, error
	Description string `json:"description,omitempty"`
	PID         string `json:"pid,omitempty"`
	CPU         string `json:"cpu,omitempty"`
	Memory      string `json:"memory,omitempty"`
	Uptime      string `json:"uptime,omitempty"`
	Type        string `json:"type"` // supervisor, systemd, docker
}

// ServiceRequest 服务操作请求
type ServiceRequest struct {
	ServerID    int    `json:"serverId"`
	ServiceName string `json:"serviceName"`
	ServiceType string `json:"serviceType"` // supervisor, systemd, docker
	Action      string `json:"action,omitempty"` // start, stop, restart
}

// ServiceResponse 服务操作响应
type ServiceResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// =============================================================================
// 认证相关数据结构
// =============================================================================

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Token   string `json:"token,omitempty"`
	User    UserInfo `json:"user,omitempty"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
}

// =============================================================================
// API响应统一结构
// =============================================================================

// APIResponse 统一API响应结构
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Details string `json:"details,omitempty"`
}

// =============================================================================
// WebSocket特定消息结构
// =============================================================================

// AuthMessage 认证消息
type AuthMessage struct {
	Type      string    `json:"type"`
	Token     string    `json:"token,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// AuthResponseMessage 认证响应消息
type AuthResponseMessage struct {
	Type    string   `json:"type"`
	Success bool     `json:"success"`
	Error   string   `json:"error,omitempty"`
	Data    UserInfo `json:"data,omitempty"`
}

// SubscribeMessage 订阅消息
type SubscribeMessage struct {
	Type     string `json:"type"`
	ServerID int    `json:"server_id"`
}

// SystemStatsBroadcastMessage 系统状态广播消息
type SystemStatsBroadcastMessage struct {
	Type      string      `json:"type"`
	ServerID  int         `json:"ServerID"`
	Data      SystemStats `json:"Data"`
	Timestamp time.Time   `json:"Timestamp"`
}

// =============================================================================
// 配置相关结构
// =============================================================================

// AppConfig 应用配置 (对应Vue3 appConfig.js)
type AppConfig struct {
	// 轮询间隔 (毫秒)
	DashboardDataFetchInterval    int `json:"dashboard_data_fetch_interval"`
	TimeUpdateInterval           int `json:"time_update_interval"`
	ServicesDataFetchInterval    int `json:"services_data_fetch_interval"`
	SystemStatsHTTPPollingInterval int `json:"system_stats_http_polling_interval"`

	// 阈值设置
	CPUWarningThreshold    float64 `json:"cpu_warning_threshold"`
	CPUOrangeThreshold     float64 `json:"cpu_orange_threshold"`
	MemoryWarningThreshold float64 `json:"memory_warning_threshold"`
	MemoryOrangeThreshold  float64 `json:"memory_orange_threshold"`

	// WebSocket设置
	WebSocketOfflineThresholdSeconds int `json:"websocket_offline_threshold_seconds"`

	// 日志查看器设置
	LogViewerWidth  int `json:"log_viewer_width"`
	LogViewerHeight int `json:"log_viewer_height"`

	// 运行时间格式化阈值 (秒)
	UptimeMinuteThreshold int64 `json:"uptime_minute_threshold"`
	UptimeHourThreshold   int64 `json:"uptime_hour_threshold"`
	UptimeDayThreshold    int64 `json:"uptime_day_threshold"`
}

// 默认配置
var DefaultAppConfig = AppConfig{
	DashboardDataFetchInterval:       60000, // 60秒
	TimeUpdateInterval:               1000,  // 1秒
	ServicesDataFetchInterval:        60000, // 60秒
	SystemStatsHTTPPollingInterval:   30000, // 30秒
	CPUWarningThreshold:              85.0,
	CPUOrangeThreshold:               60.0,
	MemoryWarningThreshold:           90.0,
	MemoryOrangeThreshold:            60.0,
	WebSocketOfflineThresholdSeconds: 60,
	LogViewerWidth:                   800,
	LogViewerHeight:                  600,
	UptimeMinuteThreshold:            60,
	UptimeHourThreshold:              3600,
	UptimeDayThreshold:               86400,
}

// =============================================================================
// 连接管理相关结构
// =============================================================================

// ClientConnection 客户端连接信息
type ClientConnection struct {
	ServerID    int       `json:"server_id"`
	LastSeen    time.Time `json:"last_seen"`
	IsConnected bool      `json:"is_connected"`
}

// FrontendConnection 前端连接信息
type FrontendConnection struct {
	UserID       string         `json:"user_id"`
	SubscribedTo map[int]bool   `json:"subscribed_to"` // 订阅的服务器ID
	LastPing     time.Time      `json:"last_ping"`
	IsConnected  bool           `json:"is_connected"`
}

// =============================================================================
// 数据验证和转换函数
// =============================================================================

// CalculateServerStatus 根据CPU和内存使用率计算服务器状态
func CalculateServerStatus(cpuUsage, memoryUsage float64, lastActive int64, config AppConfig) string {
	now := time.Now().Unix()
	timeDiff := now - lastActive

	// 检查是否离线
	if timeDiff > int64(config.WebSocketOfflineThresholdSeconds) {
		return "offline"
	}

	// 检查是否为警告状态
	if cpuUsage > config.CPUWarningThreshold || memoryUsage > config.MemoryWarningThreshold {
		return "warning"
	}

	// 检查是否为关键状态
	if cpuUsage > 95 || memoryUsage > 95 {
		return "critical"
	}

	return "online"
}

// FormatUptime 格式化运行时间
func FormatUptime(uptimeSeconds int64) string {
	if uptimeSeconds < 60 {
		return fmt.Sprintf("%d秒", uptimeSeconds)
	} else if uptimeSeconds < 3600 {
		minutes := uptimeSeconds / 60
		seconds := uptimeSeconds % 60
		return fmt.Sprintf("%d分%d秒", minutes, seconds)
	} else if uptimeSeconds < 86400 {
		hours := uptimeSeconds / 3600
		minutes := (uptimeSeconds % 3600) / 60
		return fmt.Sprintf("%d小时%d分", hours, minutes)
	} else {
		days := uptimeSeconds / 86400
		hours := (uptimeSeconds % 86400) / 3600
		return fmt.Sprintf("%d天%d小时", days, hours)
	}
}

// ConvertToServerInfo 将内部数据结构转换为前端期望的ServerInfo
func ConvertToServerInfo(serverID int, name, ip, hostname, os string, stats SystemStats, lastActive int64) ServerInfo {
	config := DefaultAppConfig
	
	return ServerInfo{
		ID:             serverID,
		Name:           name,
		IP:             ip,
		Hostname:       hostname,
		OS:             os,
		Status:         CalculateServerStatus(stats.CPU, stats.Memory, lastActive, config),
		CPU:            stats.CPU,
		Memory:         stats.Memory,
		NetInSpeed:     stats.NetInSpeed,
		NetOutSpeed:    stats.NetOutSpeed,
		NetInTransfer:  stats.NetInTransfer,
		NetOutTransfer: stats.NetOutTransfer,
		Uptime:         FormatUptime(int64(stats.Uptime)),
		LastActive:     lastActive,
		IsPlaceholder:  false,
	}
}
