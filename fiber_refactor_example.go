package main

import (
	"encoding/json"
	"log"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"
	"github.com/gofiber/websocket/v2"
)

// @title 服务器监控系统 API
// @version 1.0
// @description 高性能服务器监控系统，支持实时数据推送和WebSocket通信
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host localhost:7788
// @BasePath /api/v1
// @schemes http https
func main() {
	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		// 启用预分叉模式以提高性能
		Prefork: false,
		// 自定义错误处理
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			return c.Status(code).JSON(fiber.Map{
				"error": err.Error(),
				"code":  code,
			})
		},
	})

	// 中间件配置
	setupMiddleware(app)

	// API路由配置
	setupRoutes(app)

	// WebSocket路由配置
	setupWebSocket(app)

	// Swagger文档
	app.Get("/swagger/*", swagger.HandlerDefault)

	// 启动服务器
	log.Fatal(app.Listen(":7788"))
}

// setupMiddleware 配置中间件
func setupMiddleware(app *fiber.App) {
	// 恢复中间件
	app.Use(recover.New())

	// 日志中间件
	app.Use(logger.New(logger.Config{
		Format: "[${ip}]:${port} ${status} - ${method} ${path}\n",
	}))

	// CORS中间件
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,HEAD,PUT,DELETE,PATCH",
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
	}))
}

// setupRoutes 配置API路由
func setupRoutes(app *fiber.App) {
	// API v1 组
	api := app.Group("/api/v1")

	// 认证路由
	auth := api.Group("/auth")
	auth.Post("/login", loginHandler)
	auth.Post("/refresh", refreshTokenHandler)

	// 服务器监控路由
	servers := api.Group("/servers")
	servers.Get("/", getServersHandler)
	servers.Get("/:id/stats", getServerStatsHandler)

	// 服务管理路由
	services := api.Group("/services")
	services.Get("/list", listServicesHandler)
	services.Post("/control", controlServiceHandler)
	services.Get("/logs", getServiceLogsHandler)

	// 静态文件服务
	app.Static("/", "./web")
}

// setupWebSocket 配置WebSocket路由
func setupWebSocket(app *fiber.App) {
	// 客户端WebSocket连接（用于监控数据上报）
	app.Get("/ws", websocket.New(handleClientWebSocket))

	// 前端WebSocket连接（用于实时数据推送）
	app.Get("/ws/frontend", websocket.New(handleFrontendWebSocket))
}

// WebSocket连接管理器
type ConnectionManager struct {
	clients   map[string]*websocket.Conn
	frontends map[string]*websocket.Conn
}

var connManager = &ConnectionManager{
	clients:   make(map[string]*websocket.Conn),
	frontends: make(map[string]*websocket.Conn),
}

// handleClientWebSocket 处理客户端WebSocket连接
func handleClientWebSocket(c *websocket.Conn) {
	defer c.Close()

	for {
		var msg ClientMessage
		if err := c.ReadJSON(&msg); err != nil {
			log.Printf("客户端连接读取错误: %v", err)
			break
		}

		// 处理客户端消息
		processClientMessage(&msg)

		// 广播到前端
		broadcastToFrontends(&msg)
	}
}

// handleFrontendWebSocket 处理前端WebSocket连接
func handleFrontendWebSocket(c *websocket.Conn) {
	defer c.Close()

	// 添加到连接管理器
	connID := generateConnectionID()
	connManager.frontends[connID] = c
	defer delete(connManager.frontends, connID)

	for {
		var msg FrontendMessage
		if err := c.ReadJSON(&msg); err != nil {
			log.Printf("前端连接读取错误: %v", err)
			break
		}

		// 处理前端消息
		processFrontendMessage(c, &msg)
	}
}

// API处理函数

// loginHandler 登录处理
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param credentials body LoginRequest true "登录凭据"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Router /auth/login [post]
func loginHandler(c *fiber.Ctx) error {
	var req LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(ErrorResponse{
			Error: "无效的请求格式",
			Code:  400,
		})
	}

	// 验证用户凭据
	if req.Username == "xctcc" && req.Password == "960423Wc@" {
		token := generateJWTToken(req.Username)
		return c.JSON(LoginResponse{
			Token:     token,
			ExpiresIn: 3600,
			User:      req.Username,
		})
	}

	return c.Status(401).JSON(ErrorResponse{
		Error: "用户名或密码错误",
		Code:  401,
	})
}

// getServersHandler 获取服务器列表
// @Summary 获取服务器列表
// @Description 获取所有监控服务器的状态信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {array} ServerInfo "服务器列表"
// @Failure 401 {object} ErrorResponse "未授权"
// @Router /servers [get]
func getServersHandler(c *fiber.Ctx) error {
	// 从数据库获取服务器信息
	servers := getServersFromDB()
	return c.JSON(servers)
}

// getServerStatsHandler 获取服务器统计信息
// @Summary 获取服务器统计信息
// @Description 获取指定服务器的详细统计信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "服务器ID"
// @Success 200 {object} ServerStats "服务器统计信息"
// @Failure 404 {object} ErrorResponse "服务器不存在"
// @Router /servers/{id}/stats [get]
func getServerStatsHandler(c *fiber.Ctx) error {
	serverID := c.Params("id")
	stats := getServerStatsFromDB(serverID)
	if stats == nil {
		return c.Status(404).JSON(ErrorResponse{
			Error: "服务器不存在",
			Code:  404,
		})
	}
	return c.JSON(stats)
}

// 数据结构定义

type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type LoginResponse struct {
	Token     string `json:"token"`
	ExpiresIn int    `json:"expires_in"`
	User      string `json:"user"`
}

type ErrorResponse struct {
	Error string `json:"error"`
	Code  int    `json:"code"`
}

type ServerInfo struct {
	ID       int     `json:"id"`
	Name     string  `json:"name"`
	IP       string  `json:"ip"`
	Status   string  `json:"status"`
	CPU      float64 `json:"cpu"`
	Memory   float64 `json:"memory"`
	Uptime   string  `json:"uptime"`
}

type ServerStats struct {
	CPU        float64 `json:"cpu"`
	Memory     float64 `json:"memory"`
	Disk       float64 `json:"disk"`
	Network    NetworkStats `json:"network"`
	Processes  int     `json:"processes"`
	LoadAvg    []float64 `json:"load_avg"`
}

type NetworkStats struct {
	InSpeed  uint64 `json:"in_speed"`
	OutSpeed uint64 `json:"out_speed"`
}

type ClientMessage struct {
	Type      string      `json:"type"`
	ServerID  int         `json:"server_id"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

type FrontendMessage struct {
	Type      string      `json:"type"`
	ServerID  int         `json:"server_id,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// 辅助函数（需要实现）
func refreshTokenHandler(c *fiber.Ctx) error { return nil }
func listServicesHandler(c *fiber.Ctx) error { return nil }
func controlServiceHandler(c *fiber.Ctx) error { return nil }
func getServiceLogsHandler(c *fiber.Ctx) error { return nil }
func processClientMessage(msg *ClientMessage) {}
func broadcastToFrontends(msg *ClientMessage) {}
func processFrontendMessage(c *websocket.Conn, msg *FrontendMessage) {}
func generateConnectionID() string { return "conn_" + time.Now().Format("20060102150405") }
func generateJWTToken(username string) string { return "jwt_token_example" }
func getServersFromDB() []ServerInfo { return []ServerInfo{} }
func getServerStatsFromDB(serverID string) *ServerStats { return nil }
