# Go 服务器监控系统框架迁移指南

## 框架选择：Fiber v2

基于详细分析，推荐使用 **Fiber v2** 作为新的 Web 框架，原因如下：

### 选择理由

1. **极高性能**：基于 fasthttp，比标准 net/http 快 3-5 倍
2. **原生 WebSocket 支持**：无需额外配置，开箱即用
3. **完美 Swagger 集成**：支持 swaggo，自动生成 API 文档
4. **Express 风格 API**：语法简洁，迁移成本低
5. **丰富中间件生态**：CORS、认证、日志、恢复等
6. **零内存分配**：高性能设计理念

## 迁移步骤

### 1. 依赖管理

```bash
# 安装Fiber及相关依赖
go get github.com/gofiber/fiber/v2
go get github.com/gofiber/websocket/v2
go get github.com/gofiber/swagger
go get github.com/swaggo/swag/cmd/swag

# 安装中间件
go get github.com/gofiber/fiber/v2/middleware/cors
go get github.com/gofiber/fiber/v2/middleware/logger
go get github.com/gofiber/fiber/v2/middleware/recover
go get github.com/gofiber/fiber/v2/middleware/jwt
```

### 2. 项目结构重构

```
project/
├── cmd/
│   └── server/
│       └── main.go          # 主程序入口
├── internal/
│   ├── config/              # 配置管理
│   ├── handlers/            # HTTP处理器
│   ├── middleware/          # 自定义中间件
│   ├── models/              # 数据模型
│   ├── services/            # 业务逻辑
│   └── websocket/           # WebSocket处理
├── pkg/
│   ├── auth/                # 认证相关
│   ├── database/            # 数据库操作
│   └── utils/               # 工具函数
├── docs/                    # Swagger生成的文档
├── web/                     # 静态文件
└── go.mod
```

### 3. 核心组件迁移

#### 3.1 主程序结构

```go
// cmd/server/main.go
package main

import (
    "log"
    "github.com/gofiber/fiber/v2"
    "your-project/internal/config"
    "your-project/internal/handlers"
    "your-project/internal/middleware"
)

func main() {
    // 加载配置
    cfg := config.Load()

    // 创建Fiber应用
    app := fiber.New(fiber.Config{
        Prefork: cfg.Prefork,
        ErrorHandler: middleware.ErrorHandler,
    })

    // 设置中间件
    middleware.Setup(app)

    // 设置路由
    handlers.Setup(app)

    // 启动服务器
    log.Fatal(app.Listen(":" + cfg.Port))
}
```

#### 3.2 WebSocket 迁移

**原代码（net/http）：**

```go
// 原有的WebSocket处理
func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
    conn, err := upgrader.Upgrade(w, r, nil)
    // ...
}
```

**新代码（Fiber）：**

```go
// internal/websocket/client.go
func HandleClientConnection(c *websocket.Conn) {
    defer c.Close()

    for {
        var msg ClientMessage
        if err := c.ReadJSON(&msg); err != nil {
            log.Printf("读取消息错误: %v", err)
            break
        }

        // 处理消息
        processMessage(&msg)
    }
}

// 路由注册
app.Get("/ws", websocket.New(websocket.HandleClientConnection))
```

#### 3.3 API 路由迁移

**原代码：**

```go
http.HandleFunc("/api/servers", authMiddleware(getServersAPI))
```

**新代码：**

```go
// internal/handlers/server.go
func GetServers(c *fiber.Ctx) error {
    servers := service.GetAllServers()
    return c.JSON(servers)
}

// 路由注册
api := app.Group("/api/v1")
api.Get("/servers", middleware.Auth(), handlers.GetServers)
```

### 4. 中间件迁移

#### 4.1 认证中间件

```go
// internal/middleware/auth.go
func Auth() fiber.Handler {
    return jwt.New(jwt.Config{
        SigningKey: []byte("your-secret-key"),
        ErrorHandler: func(c *fiber.Ctx, err error) error {
            return c.Status(401).JSON(fiber.Map{
                "error": "Unauthorized",
            })
        },
    })
}
```

#### 4.2 CORS 中间件

```go
// internal/middleware/cors.go
func CORS() fiber.Handler {
    return cors.New(cors.Config{
        AllowOrigins: "*",
        AllowMethods: "GET,POST,HEAD,PUT,DELETE,PATCH",
        AllowHeaders: "Origin, Content-Type, Accept, Authorization",
    })
}
```

### 5. Swagger 集成

#### 5.1 安装 Swagger 工具

```bash
# 安装swag命令行工具
go install github.com/swaggo/swag/cmd/swag@latest

# 安装Fiber Swagger中间件
go get github.com/gofiber/swagger
```

#### 5.2 添加 Swagger 注解

**主程序注解（main.go）：**

```go
// @title 服务器监控系统 API
// @version 1.0
// @description 这是一个高性能的服务器监控系统API，支持实时数据推送和WebSocket通信
// @termsOfService http://swagger.io/terms/
// @contact.name API Support Team
// @contact.email <EMAIL>
// @license.name MIT License
// @license.url https://opensource.org/licenses/MIT
// @host localhost:7788
// @BasePath /api/v1
// @schemes http https
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
```

**API 处理函数注解：**

```go
// @Summary 获取服务器列表
// @Description 获取所有监控服务器的状态信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param status query string false "按状态过滤" Enums(online, offline, warning, critical)
// @Param limit query int false "返回数量限制" default(50) minimum(1) maximum(100)
// @Success 200 {array} models.ServerInfo "服务器列表"
// @Failure 401 {object} models.ErrorResponse "未授权访问"
// @Router /servers [get]
func GetServers(c *fiber.Ctx) error {
    // 实现逻辑
}
```

#### 5.3 数据模型注解

```go
// ServerInfo 服务器信息
type ServerInfo struct {
    ID     int     `json:"id" example:"1" description:"服务器ID"`
    Name   string  `json:"name" example:"Web-Server-01" description:"服务器名称"`
    Status string  `json:"status" example:"online" enums:"online,offline,warning,critical"`
    CPU    float64 `json:"cpu" example:"45.2" description:"CPU使用率（百分比）"`
} // @name ServerInfo
```

#### 5.4 生成和集成文档

```bash
# 生成Swagger文档
swag init -g cmd/server/main.go -o docs/

# 生成的文件：
# docs/docs.go
# docs/swagger.json
# docs/swagger.yaml
```

**集成到 Fiber 应用：**

```go
import (
    "github.com/gofiber/swagger"
    _ "your-project/docs" // 导入生成的docs包
)

func main() {
    app := fiber.New()

    // Swagger路由
    app.Get("/swagger/*", swagger.HandlerDefault)

    // 自定义Swagger配置
    app.Get("/docs/*", swagger.New(swagger.Config{
        URL:         "/docs/swagger.json",
        DeepLinking: false,
        DocExpansion: "none",
    }))
}
```

#### 5.5 高级 Swagger 配置

**自定义 Swagger UI 配置：**

```go
swaggerConfig := swagger.Config{
    URL:          "/docs/swagger.json",
    DeepLinking:  false,
    DocExpansion: "none",
    OAuth: &swagger.OAuthConfig{
        AppName:  "OAuth Provider",
        ClientId: "21bb4edc-05a7-4afc-86f1-2e151e4ba6e2",
    },
    OAuth2RedirectUrl: "http://localhost:7788/swagger/oauth2-redirect.html",
}
app.Get("/swagger/*", swagger.New(swaggerConfig))
```

**环境变量配置：**

```go
// 根据环境动态配置
if os.Getenv("ENV") == "production" {
    docs.SwaggerInfo.Host = "api.yourcompany.com"
    docs.SwaggerInfo.Schemes = []string{"https"}
} else {
    docs.SwaggerInfo.Host = "localhost:7788"
    docs.SwaggerInfo.Schemes = []string{"http"}
}
```

### 6. 性能优化配置

```go
app := fiber.New(fiber.Config{
    // 启用预分叉模式（生产环境）
    Prefork: true,

    // 禁用启动消息（生产环境）
    DisableStartupMessage: true,

    // 设置读取超时
    ReadTimeout: time.Second * 10,

    // 设置写入超时
    WriteTimeout: time.Second * 10,

    // 设置最大请求体大小
    BodyLimit: 4 * 1024 * 1024, // 4MB

    // 启用压缩
    CompressedFileSuffix: ".fiber.gz",
})
```

### 7. 测试迁移

#### 7.1 单元测试

```go
func TestGetServers(t *testing.T) {
    app := fiber.New()
    app.Get("/servers", handlers.GetServers)

    req := httptest.NewRequest("GET", "/servers", nil)
    resp, _ := app.Test(req)

    assert.Equal(t, 200, resp.StatusCode)
}
```

#### 7.2 WebSocket 测试

```go
func TestWebSocketConnection(t *testing.T) {
    app := fiber.New()
    app.Get("/ws", websocket.New(handlers.HandleWebSocket))

    // WebSocket测试逻辑
}
```

### 8. 部署配置

#### 8.1 Docker 配置

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o main cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
COPY --from=builder /app/web ./web
CMD ["./main"]
```

#### 8.2 systemd 服务

```ini
[Unit]
Description=Server Monitor
After=network.target

[Service]
Type=simple
User=monitor
WorkingDirectory=/opt/monitor
ExecStart=/opt/monitor/main
Restart=always

[Install]
WantedBy=multi-user.target
```

## 迁移检查清单

- [ ] 安装 Fiber 及相关依赖
- [ ] 重构项目结构
- [ ] 迁移 HTTP 路由处理器
- [ ] 迁移 WebSocket 处理逻辑
- [ ] 配置中间件（CORS、认证、日志等）
- [ ] 添加 Swagger 注解
- [ ] 生成 API 文档
- [ ] 编写单元测试
- [ ] 性能测试
- [ ] 部署配置

## 预期收益

1. **性能提升**：3-5 倍的 HTTP 处理性能提升
2. **内存优化**：零内存分配设计，降低 GC 压力
3. **开发效率**：Express 风格 API，开发更高效
4. **文档完善**：自动生成的 Swagger 文档
5. **维护性**：清晰的项目结构，易于维护

## 注意事项

1. **渐进式迁移**：建议分模块逐步迁移
2. **测试覆盖**：确保每个迁移的模块都有充分测试
3. **性能监控**：迁移后持续监控性能指标
4. **回滚计划**：准备回滚方案以应对意外情况
