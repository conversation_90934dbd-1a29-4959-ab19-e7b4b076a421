package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"server-monitor-fiber/internal/crypto"
	"server-monitor-fiber/internal/database"
	"strconv"
	"time"

	"github.com/gofiber/websocket/v2"
)

// 全局连接管理器
var GlobalManager = NewConnectionManager()

// 全局变量
var (
	serverStatusRepo  database.ServerStatusRepository
	serverInfoRepo    database.ServerInfoRepository
	serviceRepo       database.ServiceRepository
	webSocketPassword string = "default_password" // 应该从配置中获取
)

// 类型别名 (从database包导入)
type Message = database.Message
type ServerDetails = database.ServerDetails
type StatusInfo = database.StatusInfo

// SystemStats 系统统计信息结构
type SystemStats struct {
	CPU            float64   `json:"CPU"`
	MemUsed        uint64    `json:"MemUsed"`
	MemTotal       uint64    `json:"MemTotal"`
	Memory         float64   `json:"memory"`
	DiskUsed       uint64    `json:"DiskUsed"`
	DiskTotal      uint64    `json:"DiskTotal"`
	Disk           float64   `json:"disk"`
	NetInSpeed     uint64    `json:"NetInSpeed"`
	NetOutSpeed    uint64    `json:"NetOutSpeed"`
	NetInTransfer  uint64    `json:"NetInTransfer"`
	NetOutTransfer uint64    `json:"NetOutTransfer"`
	Uptime         uint64    `json:"Uptime"`
	LoadAvg        []float64 `json:"LoadAvg,omitempty"`
	Processes      int       `json:"Processes,omitempty"`
}

// AuthMessage 认证消息
type AuthMessage struct {
	Token string `json:"token,omitempty"`
}

// SubscribeMessage 订阅消息
type SubscribeMessage struct {
	ServerID int `json:"server_id"`
}

// SystemStatsBroadcastMessage 系统状态广播消息
type SystemStatsBroadcastMessage struct {
	ServerID int         `json:"ServerID"`
	Data     SystemStats `json:"Data"`
}

// HandleClientWebSocket 处理客户端WebSocket连接 (迁移自原main.go handleWebSocket)
func HandleClientWebSocket(c *websocket.Conn) {
	defer c.Close()

	log.Printf("客户端连接: %s", c.RemoteAddr())

	// 从查询参数获取服务器ID (可选)
	serverIDStr := c.Query("id")
	var serverID int
	if serverIDStr != "" {
		var err error
		serverID, err = strconv.Atoi(serverIDStr)
		if err != nil {
			log.Printf("无效的服务器ID: %s", serverIDStr)
		}
	}

	// 设置读取超时
	c.SetReadDeadline(time.Now().Add(60 * time.Second))

	for {
		var msg Message
		err := c.ReadJSON(&msg)
		if err != nil {
			log.Printf("读取错误: %v", err)
			break
		}

		// 验证密码 (从配置中获取)
		expectedPassword := getWebSocketPassword()
		if msg.Password != expectedPassword {
			log.Printf("无效密码来自 %s", c.RemoteAddr())
			continue
		}

		var serverDetails ServerDetails

		if msg.Encrypted && msg.EncryptedData != "" {
			// 解密数据
			decryptedData, err := decryptData(msg.EncryptedData, msg.Password)
			if err != nil {
				log.Printf("解密数据失败来自 %s: %v", c.RemoteAddr(), err)
				continue
			}

			// 解析解密数据
			err = json.Unmarshal(decryptedData, &serverDetails)
			if err != nil {
				log.Printf("解析解密数据失败来自 %s: %v", c.RemoteAddr(), err)
				continue
			}
		} else {
			// 使用未加密数据（向后兼容）
			serverDetails = msg.Data
		}

		// 更新最后活跃时间
		serverDetails.LastActive = time.Now().Unix()

		// 如果从查询参数获取了服务器ID，使用它
		if serverID > 0 {
			serverDetails.ID = serverID
		}

		// 处理服务器数据
		err = processServerData(&serverDetails, &msg)
		if err != nil {
			log.Printf("处理服务器数据失败: %v", err)
		}

		// 添加到连接管理器
		GlobalManager.AddClientConnection(serverDetails.ID, c)
		GlobalManager.UpdateClientLastSeen(serverDetails.ID)

		// 重置读取超时
		c.SetReadDeadline(time.Now().Add(60 * time.Second))
	}

	log.Printf("客户端断开连接: %s", c.RemoteAddr())
}

// HandleFrontendWebSocket 处理前端WebSocket连接
func HandleFrontendWebSocket(c *websocket.Conn) {
	defer c.Close()

	// 生成连接ID
	connID := fmt.Sprintf("frontend_%d", time.Now().UnixNano())
	log.Printf("前端WebSocket连接建立: %s", connID)

	// 添加到连接管理器
	GlobalManager.AddFrontendConnection(connID, c)
	defer GlobalManager.RemoveFrontendConnection(connID)

	// 设置读取超时
	c.SetReadDeadline(time.Now().Add(60 * time.Second))

	for {
		var message WSMessage
		if err := c.ReadJSON(&message); err != nil {
			log.Printf("前端连接读取错误 (%s): %v", connID, err)
			break
		}

		// 更新最后ping时间
		GlobalManager.UpdateFrontendLastPing(connID)

		// 重置读取超时
		c.SetReadDeadline(time.Now().Add(60 * time.Second))

		log.Printf("收到前端消息 (%s): %s", connID, message.Type)

		// 处理不同类型的消息
		switch message.Type {
		case WSMsgTypeAuth:
			handleFrontendAuth(c, connID, message)
		case WSMsgTypeSubscribe:
			handleFrontendSubscribe(connID, message)
		case WSMsgTypePing:
			handleFrontendPing(c, connID)
		default:
			log.Printf("未知的前端消息类型: %s", message.Type)
		}
	}
}

// handleSystemStatsMessage 处理系统统计消息
func handleSystemStatsMessage(serverID int, rawMessage map[string]interface{}) {
	// 解析系统统计数据
	dataInterface, ok := rawMessage["data"]
	if !ok {
		log.Printf("系统统计消息缺少data字段")
		return
	}

	// 转换为JSON再解析为SystemStats
	dataBytes, err := json.Marshal(dataInterface)
	if err != nil {
		log.Printf("序列化系统统计数据失败: %v", err)
		return
	}

	var stats SystemStats
	if err := json.Unmarshal(dataBytes, &stats); err != nil {
		log.Printf("解析系统统计数据失败: %v", err)
		return
	}

	// 创建广播消息
	broadcastMsg := WSMessage{
		Type:     WSMsgTypeSystemStatsBroadcast,
		ServerID: serverID,
		Data: SystemStatsBroadcastMessage{
			ServerID: serverID,
			Data:     stats,
		},
	}

	// 广播到所有订阅的前端
	GlobalManager.BroadcastToFrontends(serverID, broadcastMsg)

	log.Printf("广播服务器 %d 的系统统计数据: CPU=%.1f%%, Memory=%.1f%%",
		serverID, stats.CPU, stats.Memory)
}

// handleClientPing 处理客户端ping
func handleClientPing(c *websocket.Conn, serverID int) {
	response := WSMessage{
		Type:      WSMsgTypePong,
		Timestamp: time.Now(),
	}

	if err := c.WriteJSON(response); err != nil {
		log.Printf("发送pong到客户端失败 (服务器ID %d): %v", serverID, err)
	}
}

// handleFrontendAuth 处理前端认证
func handleFrontendAuth(c *websocket.Conn, connID string, message WSMessage) {
	// 简单的认证逻辑（实际应该验证token）
	var authData AuthMessage
	if message.Data != nil {
		dataBytes, _ := json.Marshal(message.Data)
		json.Unmarshal(dataBytes, &authData)
	}

	// 这里应该验证token，暂时简单处理
	success := true // 或者验证authData.Token

	response := WSMessage{
		Type: WSMsgTypeAuthResponse,
		Data: map[string]interface{}{
			"success": success,
			"user":    "authenticated_user",
		},
		Timestamp: time.Now(),
	}

	if err := c.WriteJSON(response); err != nil {
		log.Printf("发送认证响应失败 (%s): %v", connID, err)
	} else {
		log.Printf("前端认证成功: %s", connID)
	}
}

// handleFrontendSubscribe 处理前端订阅
func handleFrontendSubscribe(connID string, message WSMessage) {
	var subData SubscribeMessage
	if message.Data != nil {
		dataBytes, _ := json.Marshal(message.Data)
		json.Unmarshal(dataBytes, &subData)
	}

	// 如果消息中有ServerID，使用它；否则使用Data中的server_id
	serverID := message.ServerID
	if serverID == 0 {
		serverID = subData.ServerID
	}

	if serverID > 0 {
		GlobalManager.SubscribeToServer(connID, serverID)
		log.Printf("前端 %s 订阅服务器 %d", connID, serverID)
	} else {
		log.Printf("前端订阅消息缺少有效的服务器ID: %+v", message)
	}
}

// handleFrontendPing 处理前端ping
func handleFrontendPing(c *websocket.Conn, connID string) {
	response := WSMessage{
		Type:      WSMsgTypePong,
		Timestamp: time.Now(),
	}

	if err := c.WriteJSON(response); err != nil {
		log.Printf("发送pong到前端失败 (%s): %v", connID, err)
	}
}

// StartWebSocketManager 启动WebSocket管理器
func StartWebSocketManager() {
	log.Println("启动WebSocket连接管理器")

	// 启动定期清理协程，每30秒检查一次，超时时间为2分钟
	GlobalManager.StartCleanupRoutine(30*time.Second, 2*time.Minute)

	log.Println("WebSocket连接管理器已启动")
}

// GetConnectionStats 获取连接统计信息
func GetConnectionStats() map[string]interface{} {
	return GlobalManager.GetStats()
}

// BroadcastToAllFrontends 向所有前端广播消息
func BroadcastToAllFrontends(message WSMessage) {
	connections := GlobalManager.GetFrontendConnections()

	message.Timestamp = time.Now()
	messageData, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化广播消息失败: %v", err)
		return
	}

	broadcastCount := 0
	for connID, conn := range connections {
		if err := conn.Conn.WriteMessage(websocket.TextMessage, messageData); err != nil {
			log.Printf("向前端 %s 发送消息失败: %v", connID, err)
			go GlobalManager.RemoveFrontendConnection(connID)
		} else {
			broadcastCount++
		}
	}

	log.Printf("向 %d 个前端连接广播消息", broadcastCount)
}

// =============================================================================
// 辅助函数 (迁移自原main.go)
// =============================================================================

// getWebSocketPassword 获取WebSocket密码
func getWebSocketPassword() string {
	return webSocketPassword
}

// SetWebSocketPassword 设置WebSocket密码
func SetWebSocketPassword(password string) {
	webSocketPassword = password
}

// SetRepositories 设置数据库仓库
func SetRepositories(statusRepo database.ServerStatusRepository, infoRepo database.ServerInfoRepository, svcRepo database.ServiceRepository) {
	serverStatusRepo = statusRepo
	serverInfoRepo = infoRepo
	serviceRepo = svcRepo
}

// decryptData 解密数据
func decryptData(encryptedData, password string) ([]byte, error) {
	return crypto.Decrypt(encryptedData, password)
}

// processServerData 处理服务器数据 (迁移自原main.go)
func processServerData(serverDetails *ServerDetails, msg *Message) error {
	// 创建或更新 ServerStatus
	serverStatus := serverDetails.ToServerStatus()
	if err := serverStatusRepo.SaveServerStatus(serverStatus); err != nil {
		log.Printf("数据库错误 (ServerStatus): %v", err)
		return err
	}

	// 更新内存缓存
	updateServerStatusCache(serverStatus.ID, serverStatus.ToStatusInfo())

	// 创建或更新 ServerInfo
	serverInfo := serverDetails.ToServerInfo()
	if err := serverInfoRepo.UpdateServerInfo(serverInfo); err != nil {
		log.Printf("数据库错误 (ServerInfo): %v", err)
	}

	// 保存历史监控数据
	serverStatusHistory := serverDetails.ToServerStatusHistory()
	if err := serverStatusRepo.CreateServerStatusHistory(serverStatusHistory); err != nil {
		log.Printf("数据库错误 (ServerStatusHistory): %v", err)
	}

	// 异步处理服务数据
	go serviceRepo.ProcessServiceUpdates(serverDetails.ID, msg.SupervisorServices, msg.SystemdServices, msg.DockerServices)

	// 广播到前端
	broadcastSystemStats(serverDetails.ID, serverStatus.ToStatusInfo())

	return nil
}

// updateServerStatusCache 更新服务器状态缓存
func updateServerStatusCache(serverID int, statusInfo *StatusInfo) {
	// 这里应该更新内存缓存
	// 暂时省略，可以后续实现
}

// broadcastSystemStats 广播系统统计信息到前端
func broadcastSystemStats(serverID int, statusInfo *StatusInfo) {
	// 创建广播消息
	broadcastMsg := WSMessage{
		Type:     WSMsgTypeSystemStatsBroadcast,
		ServerID: serverID,
		Data: SystemStatsBroadcastMessage{
			ServerID: serverID,
			Data: SystemStats{
				CPU:         statusInfo.CPU,
				MemUsed:     statusInfo.MemUsed,
				MemTotal:    statusInfo.MemTotal,
				Memory:      calculateMemoryPercentage(statusInfo.MemUsed, statusInfo.MemTotal),
				DiskUsed:    statusInfo.DiskUsed,
				DiskTotal:   statusInfo.DiskTotal,
				Disk:        calculateDiskPercentage(statusInfo.DiskUsed, statusInfo.DiskTotal),
				NetInSpeed:  statusInfo.NetInSpeed,
				NetOutSpeed: statusInfo.NetOutSpeed,
				Uptime:      statusInfo.Uptime,
				LoadAvg:     []float64{statusInfo.Load1, statusInfo.Load5, statusInfo.Load15},
			},
		},
	}

	// 广播到所有订阅的前端
	GlobalManager.BroadcastToFrontends(serverID, broadcastMsg)
}

// calculateMemoryPercentage 计算内存使用百分比
func calculateMemoryPercentage(used, total uint64) float64 {
	if total == 0 {
		return 0
	}
	return (float64(used) / float64(total)) * 100
}

// calculateDiskPercentage 计算磁盘使用百分比
func calculateDiskPercentage(used, total uint64) float64 {
	if total == 0 {
		return 0
	}
	return (float64(used) / float64(total)) * 100
}
