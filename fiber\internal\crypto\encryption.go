package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"io"

	"golang.org/x/crypto/pbkdf2"
)

// =============================================================================
// 加密解密功能 (迁移自原main.go)
// =============================================================================

const (
	// PBKDF2参数
	saltSize   = 32
	keySize    = 32
	iterations = 10000
)

// Encrypt 加密数据
func Encrypt(data []byte, password string) (string, error) {
	// 生成随机盐
	salt := make([]byte, saltSize)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return "", err
	}

	// 使用PBKDF2派生密钥
	key := pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)

	// 创建AES加密器
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 使用GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, data, nil)

	// 组合盐和密文
	result := append(salt, ciphertext...)

	// 返回base64编码的结果
	return base64.StdEncoding.EncodeToString(result), nil
}

// Decrypt 解密数据
func Decrypt(encryptedData string, password string) ([]byte, error) {
	// base64解码
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}

	// 检查数据长度
	if len(data) < saltSize {
		return nil, errors.New("encrypted data too short")
	}

	// 提取盐
	salt := data[:saltSize]
	ciphertext := data[saltSize:]

	// 使用PBKDF2派生密钥
	key := pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)

	// 创建AES解密器
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 使用GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// 检查密文长度
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, errors.New("ciphertext too short")
	}

	// 提取nonce和实际密文
	nonce := ciphertext[:nonceSize]
	ciphertext = ciphertext[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// GenerateKey 生成随机密钥
func GenerateKey() (string, error) {
	key := make([]byte, 32)
	if _, err := io.ReadFull(rand.Reader, key); err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

// HashPassword 哈希密码
func HashPassword(password string) string {
	hash := sha256.Sum256([]byte(password))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// VerifyPassword 验证密码
func VerifyPassword(password, hash string) bool {
	return HashPassword(password) == hash
}

// =============================================================================
// 辅助函数
// =============================================================================

// IsEncrypted 检查数据是否已加密
func IsEncrypted(data string) bool {
	// 尝试base64解码
	decoded, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return false
	}

	// 检查长度是否符合加密数据格式
	return len(decoded) > saltSize+12 // 盐长度 + 最小GCM密文长度
}

// SecureCompare 安全比较两个字符串
func SecureCompare(a, b string) bool {
	if len(a) != len(b) {
		return false
	}

	result := 0
	for i := 0; i < len(a); i++ {
		result |= int(a[i]) ^ int(b[i])
	}

	return result == 0
}
