# 服务器监控系统 Fiber 版本部署指南

## 🚀 快速部署

### 环境要求

- **Go**: 1.21 或更高版本
- **操作系统**: Linux, macOS, Windows
- **内存**: 最少 512MB，推荐 1GB+
- **磁盘**: 最少 100MB 可用空间

### 1. 克隆和构建

```bash
# 进入项目目录
cd fiber

# 安装依赖
make deps

# 生成API文档
make swagger

# 构建应用
make build
```

### 2. 配置文件

创建服务器配置文件 `../server.json`：

```json
{
  "listen": "0.0.0.0",
  "port": "7788",
  "login_username": "xctcc",
  "login_password": "960423Wc@",
  "websocket": {
    "enabled": true,
    "path": "/ws"
  },
  "database": {
    "type": "sqlite",
    "path": "./monitor.db"
  },
  "secure_cookie": false,
  "password": "your-websocket-password",
  "servers": [
    {
      "mid": 1,
      "name": "服务器1",
      "host": "*************",
      "enabled": true
    }
  ]
}
```

### 3. 启动服务

```bash
# 开发模式
make dev

# 生产模式
./build/server-monitor-fiber -s -f ../server.json
```

## 🐳 Docker 部署

### Dockerfile

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o server-monitor-fiber cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/server-monitor-fiber .
COPY --from=builder /app/web ./web

EXPOSE 7788
CMD ["./server-monitor-fiber", "-s"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  monitor-server:
    build: .
    ports:
      - "7788:7788"
    volumes:
      - ./data:/data
      - ./config:/config
    environment:
      - DATABASE_PATH=/data/monitor.db
      - CONFIG_PATH=/config/server.json
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - monitor-server
    restart: unless-stopped
```

## 🔧 生产环境配置

### 1. 反向代理 (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    location / {
        proxy_pass http://127.0.0.1:7788;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws {
        proxy_pass http://127.0.0.1:7788;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. Systemd 服务

创建 `/etc/systemd/system/monitor-server.service`：

```ini
[Unit]
Description=Server Monitor Fiber
After=network.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/monitor
ExecStart=/opt/monitor/server-monitor-fiber -s -f /opt/monitor/server.json
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=monitor-server

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/monitor/data

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable monitor-server
sudo systemctl start monitor-server
sudo systemctl status monitor-server
```

### 3. 环境变量配置

```bash
# 生产环境变量
export ENVIRONMENT=production
export PORT=7788
export DATABASE_URL=/opt/monitor/data/monitor.db
export JWT_SECRET=your-super-secret-key
export SECURE_COOKIE=true
export LOG_LEVEL=info
export RATE_LIMIT_ENABLED=true
export RATE_LIMIT_RPS=100
```

## 📊 监控和日志

### 1. 日志配置

```bash
# 查看实时日志
journalctl -u monitor-server -f

# 查看错误日志
journalctl -u monitor-server -p err

# 日志轮转配置
sudo nano /etc/logrotate.d/monitor-server
```

### 2. 健康检查

```bash
# 健康检查脚本
#!/bin/bash
curl -f http://localhost:7788/api/health || exit 1
```

### 3. 性能监控

```bash
# 监控脚本
#!/bin/bash
echo "=== 服务状态 ==="
systemctl status monitor-server

echo "=== 内存使用 ==="
ps aux | grep server-monitor-fiber

echo "=== 连接数 ==="
netstat -an | grep :7788 | wc -l

echo "=== 磁盘使用 ==="
df -h /opt/monitor
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# UFW 配置
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# iptables 配置
iptables -A INPUT -p tcp --dport 7788 -s 127.0.0.1 -j ACCEPT
iptables -A INPUT -p tcp --dport 7788 -j DROP
```

### 2. SSL/TLS 配置

```bash
# 使用 Let's Encrypt
sudo certbot --nginx -d your-domain.com

# 或者使用自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/nginx/ssl/key.pem \
  -out /etc/nginx/ssl/cert.pem
```

### 3. 数据库安全

```bash
# 设置数据库文件权限
chmod 600 /opt/monitor/data/monitor.db
chown monitor:monitor /opt/monitor/data/monitor.db
```

## 📈 性能优化

### 1. Go 应用优化

```bash
# 构建优化版本
go build -ldflags="-s -w" -o server-monitor-fiber cmd/server/main.go

# 启用 PGO (Profile-Guided Optimization)
go build -pgo=auto -o server-monitor-fiber cmd/server/main.go
```

### 2. 系统优化

```bash
# 增加文件描述符限制
echo "monitor soft nofile 65536" >> /etc/security/limits.conf
echo "monitor hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

## 🔄 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# 备份脚本
BACKUP_DIR="/backup/monitor"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
cp /opt/monitor/data/monitor.db $BACKUP_DIR/monitor_$DATE.db

# 备份配置
cp /opt/monitor/server.json $BACKUP_DIR/server_$DATE.json

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "monitor_*.db" -mtime +7 -delete
```

### 2. 自动备份

```bash
# 添加到 crontab
0 2 * * * /opt/monitor/backup.sh
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo lsof -i :7788
   sudo netstat -tulpn | grep :7788
   ```

2. **数据库权限问题**
   ```bash
   sudo chown -R monitor:monitor /opt/monitor/data
   sudo chmod 755 /opt/monitor/data
   sudo chmod 644 /opt/monitor/data/monitor.db
   ```

3. **WebSocket 连接失败**
   - 检查防火墙设置
   - 验证反向代理配置
   - 确认 WebSocket 升级头部

4. **内存泄漏**
   ```bash
   # 监控内存使用
   watch -n 5 'ps aux | grep server-monitor-fiber'
   
   # 生成内存分析
   go tool pprof http://localhost:7788/debug/pprof/heap
   ```

### 日志分析

```bash
# 错误日志分析
journalctl -u monitor-server | grep ERROR

# 性能分析
journalctl -u monitor-server | grep "slow query"

# 连接分析
journalctl -u monitor-server | grep "WebSocket"
```

## 📞 支持

如果遇到问题，请：

1. 检查日志文件
2. 验证配置文件
3. 确认网络连接
4. 查看系统资源使用情况

更多信息请参考项目文档或提交 Issue。
