# 服务器监控系统 Makefile
# 用于自动化构建、测试、文档生成等任务

# 变量定义
APP_NAME := server-monitor
VERSION := $(shell git describe --tags --always --dirty)
BUILD_TIME := $(shell date +%Y-%m-%dT%H:%M:%S)
GO_VERSION := $(shell go version | awk '{print $$3}')
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 目录定义
BUILD_DIR := build
DOCS_DIR := docs
CMD_DIR := cmd/server
INTERNAL_DIR := internal

# Go相关变量
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod
GOFMT := $(GOCMD) fmt

# Swagger相关
SWAG := swag
SWAG_INIT := $(SWAG) init
SWAG_FMT := $(SWAG) fmt

# Docker相关
DOCKER := docker
DOCKER_BUILD := $(DOCKER) build
DOCKER_RUN := $(DOCKER) run

.PHONY: all build clean test deps swagger swagger-fmt run dev help

# 默认目标
all: clean deps swagger build

# 帮助信息
help:
	@echo "服务器监控系统构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build          构建应用程序"
	@echo "  clean          清理构建文件"
	@echo "  test           运行测试"
	@echo "  deps           安装依赖"
	@echo "  swagger        生成Swagger文档"
	@echo "  swagger-fmt    格式化Swagger注释"
	@echo "  run            运行应用程序"
	@echo "  dev            开发模式运行"
	@echo "  docker-build   构建Docker镜像"
	@echo "  docker-run     运行Docker容器"
	@echo "  lint           代码检查"
	@echo "  fmt            格式化代码"
	@echo "  install-tools  安装开发工具"

# 安装开发工具
install-tools:
	@echo "安装开发工具..."
	@go install github.com/swaggo/swag/cmd/swag@latest
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/air-verse/air@latest
	@echo "开发工具安装完成"

# 安装依赖
deps:
	@echo "安装Go依赖..."
	@$(GOMOD) download
	@$(GOMOD) tidy
	@echo "依赖安装完成"

# 格式化代码
fmt:
	@echo "格式化Go代码..."
	@$(GOFMT) ./...
	@echo "代码格式化完成"

# 代码检查
lint:
	@echo "运行代码检查..."
	@golangci-lint run ./...
	@echo "代码检查完成"

# 格式化Swagger注释
swagger-fmt:
	@echo "格式化Swagger注释..."
	@$(SWAG_FMT) -d $(CMD_DIR) -g main.go
	@echo "Swagger注释格式化完成"

# 生成Swagger文档
swagger: swagger-fmt
	@echo "生成Swagger文档..."
	@$(SWAG_INIT) -g $(CMD_DIR)/main.go -o $(DOCS_DIR) --parseDependency --parseInternal
	@echo "Swagger文档生成完成"
	@echo "文档访问地址: http://localhost:7788/swagger/"

# 验证Swagger文档
swagger-validate:
	@echo "验证Swagger文档..."
	@if [ -f $(DOCS_DIR)/swagger.json ]; then \
		echo "swagger.json 文件存在"; \
		echo "文档大小: $$(du -h $(DOCS_DIR)/swagger.json | cut -f1)"; \
	else \
		echo "错误: swagger.json 文件不存在，请先运行 make swagger"; \
		exit 1; \
	fi

# 运行测试
test:
	@echo "运行测试..."
	@$(GOTEST) -v -race -coverprofile=coverage.out ./...
	@$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "测试完成，覆盖率报告: coverage.html"

# 基准测试
benchmark:
	@echo "运行基准测试..."
	@$(GOTEST) -bench=. -benchmem ./...

# 构建应用程序
build: swagger
	@echo "构建应用程序..."
	@mkdir -p $(BUILD_DIR)
	@$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) $(CMD_DIR)/main.go
	@echo "构建完成: $(BUILD_DIR)/$(APP_NAME)"

# 构建多平台版本
build-all: swagger
	@echo "构建多平台版本..."
	@mkdir -p $(BUILD_DIR)
	
	# Linux AMD64
	@GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-amd64 $(CMD_DIR)/main.go
	
	# Linux ARM64
	@GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-arm64 $(CMD_DIR)/main.go
	
	# Windows AMD64
	@GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe $(CMD_DIR)/main.go
	
	# macOS AMD64
	@GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-amd64 $(CMD_DIR)/main.go
	
	# macOS ARM64
	@GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-arm64 $(CMD_DIR)/main.go
	
	@echo "多平台构建完成"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	@$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@echo "清理完成"

# 运行应用程序（服务器模式）
run: build
	@echo "启动服务器..."
	@$(BUILD_DIR)/$(APP_NAME) -s

# 运行应用程序（客户端模式）
run-client: build
	@echo "启动客户端..."
	@$(BUILD_DIR)/$(APP_NAME) -c

# 开发模式运行（热重载）
dev:
	@echo "启动开发模式（热重载）..."
	@air -c .air.toml

# 生成开发配置文件
dev-config:
	@echo "生成开发配置文件..."
	@cat > .air.toml << 'EOF'
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = ["-s"]
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main cmd/server/main.go"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "build", "docs"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
EOF
	@echo "开发配置文件生成完成: .air.toml"

# Docker构建
docker-build:
	@echo "构建Docker镜像..."
	@$(DOCKER_BUILD) -t $(APP_NAME):$(VERSION) .
	@$(DOCKER_BUILD) -t $(APP_NAME):latest .
	@echo "Docker镜像构建完成"

# Docker运行
docker-run:
	@echo "运行Docker容器..."
	@$(DOCKER_RUN) -d -p 7788:7788 --name $(APP_NAME) $(APP_NAME):latest

# Docker清理
docker-clean:
	@echo "清理Docker资源..."
	@$(DOCKER) stop $(APP_NAME) || true
	@$(DOCKER) rm $(APP_NAME) || true
	@$(DOCKER) rmi $(APP_NAME):latest || true
	@$(DOCKER) rmi $(APP_NAME):$(VERSION) || true

# 生成版本信息
version:
	@echo "应用版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Go版本: $(GO_VERSION)"

# 检查依赖更新
deps-update:
	@echo "检查依赖更新..."
	@$(GOCMD) list -u -m all

# 安全检查
security:
	@echo "运行安全检查..."
	@gosec ./...

# 生成发布包
release: clean build-all swagger
	@echo "生成发布包..."
	@mkdir -p $(BUILD_DIR)/release
	@cp -r $(DOCS_DIR) $(BUILD_DIR)/release/
	@cp -r web $(BUILD_DIR)/release/
	@cp README.md $(BUILD_DIR)/release/
	@cp MIGRATION_GUIDE.md $(BUILD_DIR)/release/
	@cd $(BUILD_DIR) && tar -czf release/$(APP_NAME)-$(VERSION).tar.gz $(APP_NAME)-*
	@echo "发布包生成完成: $(BUILD_DIR)/release/$(APP_NAME)-$(VERSION).tar.gz"

# 部署到服务器
deploy:
	@echo "部署到服务器..."
	@echo "请根据实际情况修改部署脚本"
	# @scp $(BUILD_DIR)/$(APP_NAME) user@server:/opt/$(APP_NAME)/
	# @ssh user@server "sudo systemctl restart $(APP_NAME)"

# 监控日志
logs:
	@echo "查看应用日志..."
	@tail -f /var/log/$(APP_NAME).log

# 健康检查
health:
	@echo "执行健康检查..."
	@curl -f http://localhost:7788/api/v1/health || echo "健康检查失败"

# 性能测试
perf:
	@echo "运行性能测试..."
	@go test -bench=. -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof ./...
	@echo "性能测试完成，查看 cpu.prof 和 mem.prof 文件"
