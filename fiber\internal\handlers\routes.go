package handlers

import (
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
)

// SetupRoutes 设置所有路由
func SetupRoutes(app *fiber.App) {
	// 设置API路由
	setupAPIRoutes(app)
	
	// 设置WebSocket路由
	setupWebSocketRoutes(app)
}

// setupAPIRoutes 设置API路由
func setupAPIRoutes(app *fiber.App) {
	// API组
	api := app.Group("/api")

	// 健康检查
	api.Get("/health", HealthCheckHandler)

	// 认证路由
	auth := api.Group("/auth")
	auth.Post("/login", LoginHandler)
	auth.Post("/logout", LogoutHandler)

	// 服务器监控路由
	servers := api.Group("/servers")
	servers.Get("/", GetServersHandler)
	servers.Get("/:id", GetServerByIDHandler)

	// 系统统计路由
	system := api.Group("/system")
	system.Get("/stats", GetSystemStatsHandler)

	// 服务管理路由
	services := api.Group("/services")
	services.Get("/list", GetServicesListHandler)
	services.Post("/:type/:action", ServiceActionHandler)
	services.Get("/logs", GetServiceLogsHandler)
}

// setupWebSocketRoutes 设置WebSocket路由
func setupWebSocketRoutes(app *fiber.App) {
	// 启动WebSocket管理器
	StartWebSocketManager()

	// 客户端WebSocket连接（用于监控数据上报）
	app.Get("/ws", websocket.New(HandleClientWebSocket))

	// 前端WebSocket连接（用于实时数据推送）
	app.Get("/ws/frontend", websocket.New(HandleFrontendWebSocket))
}

// WebSocket处理器占位符 - 将在websocket包中实现
func HandleClientWebSocket(c *websocket.Conn) {
	// 这里将调用websocket包中的实际处理器
	// wsmanager.HandleClientWebSocket(c)
}

func HandleFrontendWebSocket(c *websocket.Conn) {
	// 这里将调用websocket包中的实际处理器
	// wsmanager.HandleFrontendWebSocket(c)
}

func StartWebSocketManager() {
	// 这里将调用websocket包中的启动函数
	// wsmanager.StartWebSocketManager()
}
