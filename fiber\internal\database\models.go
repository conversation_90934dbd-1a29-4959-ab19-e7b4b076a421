package database

import (
	"time"
)

// =============================================================================
// 数据库模型定义 (迁移自原main.go和hertz_project/pkg/model)
// =============================================================================

// ServerStatus 服务器状态表
type ServerStatus struct {
	ID                int     `gorm:"primaryKey" json:"id"`
	Name              string  `gorm:"size:255" json:"name"`
	Tag               string  `gorm:"size:255" json:"tag"`
	LastActive        int64   `json:"last_active"`
	IPv4              string  `gorm:"size:45" json:"ipv4"`
	IPv6              string  `gorm:"size:45" json:"ipv6"`
	ValidIP           string  `gorm:"size:45" json:"valid_ip"`
	StatusCPU         float64 `json:"status_cpu"`
	StatusMemUsed     uint64  `json:"status_mem_used"`
	StatusMemTotal    uint64  `json:"status_mem_total"`
	StatusDiskUsed    uint64  `json:"status_disk_used"`
	StatusDiskTotal   uint64  `json:"status_disk_total"`
	StatusNetInSpeed  uint64  `json:"status_net_in_speed"`
	StatusNetOutSpeed uint64  `json:"status_net_out_speed"`
	StatusUptime      uint64  `json:"status_uptime"`
	StatusLoad1       float64 `json:"status_load1"`
	StatusLoad5       float64 `json:"status_load5"`
	StatusLoad15      float64 `json:"status_load15"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ServerInfo 服务器信息表
type ServerInfo struct {
	ID                  int    `gorm:"primaryKey" json:"id"`
	HostPlatform        string `gorm:"size:255" json:"host_platform"`
	HostPlatformVersion string `gorm:"size:255" json:"host_platform_version"`
	HostCPU             string `gorm:"size:255" json:"host_cpu"`
	HostArch            string `gorm:"size:255" json:"host_arch"`
	HostVirtualization  string `gorm:"size:255" json:"host_virtualization"`
	HostBootTime        uint64 `json:"host_boot_time"`
	HostCountryCode     string `gorm:"size:10" json:"host_country_code"`
	HostVersion         string `gorm:"size:255" json:"host_version"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// ServerStatusHistory 服务器状态历史表
type ServerStatusHistory struct {
	ID                uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ServerID          int       `gorm:"index" json:"server_id"`
	Timestamp         time.Time `gorm:"index" json:"timestamp"`
	StatusCPU         float64   `json:"status_cpu"`
	StatusMemUsed     uint64    `json:"status_mem_used"`
	StatusMemTotal    uint64    `json:"status_mem_total"`
	StatusDiskUsed    uint64    `json:"status_disk_used"`
	StatusDiskTotal   uint64    `json:"status_disk_total"`
	StatusNetInSpeed  uint64    `json:"status_net_in_speed"`
	StatusNetOutSpeed uint64    `json:"status_net_out_speed"`
	StatusUptime      uint64    `json:"status_uptime"`
	StatusLoad1       float64   `json:"status_load1"`
	StatusLoad5       float64   `json:"status_load5"`
	StatusLoad15      float64   `json:"status_load15"`
}

// SupervisorService Supervisor服务表
type SupervisorService struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ServerID    int       `gorm:"index" json:"server_id"`
	Name        string    `gorm:"size:255;index" json:"name"`
	Status      string    `gorm:"size:50" json:"status"`
	Description string    `gorm:"type:text" json:"description"`
	PID         string    `gorm:"size:50" json:"pid"`
	Uptime      string    `gorm:"size:255" json:"uptime"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// SystemdService Systemd服务表
type SystemdService struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ServerID    int       `gorm:"index" json:"server_id"`
	Name        string    `gorm:"size:255;index" json:"name"`
	Status      string    `gorm:"size:50" json:"status"`
	Description string    `gorm:"type:text" json:"description"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// DockerService Docker服务表
type DockerService struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ServerID    int       `gorm:"index" json:"server_id"`
	Name        string    `gorm:"size:255;index" json:"name"`
	Status      string    `gorm:"size:50" json:"status"`
	Image       string    `gorm:"size:255" json:"image"`
	Ports       string    `gorm:"size:255" json:"ports"`
	CreatedTime string    `gorm:"size:255" json:"created_time"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// =============================================================================
// 业务模型 (用于WebSocket通信)
// =============================================================================

// ServerDetails 服务器详细信息 (WebSocket消息格式)
type ServerDetails struct {
	ID         int        `json:"id"`
	Name       string     `json:"name"`
	Tag        string     `json:"tag"`
	LastActive int64      `json:"last_active"`
	IPv4       string     `json:"ipv4"`
	IPv6       string     `json:"ipv6"`
	ValidIP    string     `json:"valid_ip"`
	Status     StatusInfo `json:"status"`
	Host       HostInfo   `json:"host"`
}

// StatusInfo 状态信息
type StatusInfo struct {
	CPU         float64 `json:"CPU"`
	MemUsed     uint64  `json:"MemUsed"`
	MemTotal    uint64  `json:"MemTotal"`
	DiskUsed    uint64  `json:"DiskUsed"`
	DiskTotal   uint64  `json:"DiskTotal"`
	NetInSpeed  uint64  `json:"NetInSpeed"`
	NetOutSpeed uint64  `json:"NetOutSpeed"`
	Uptime      uint64  `json:"Uptime"`
	Load1       float64 `json:"Load1"`
	Load5       float64 `json:"Load5"`
	Load15      float64 `json:"Load15"`
}

// HostInfo 主机信息
type HostInfo struct {
	Platform        string `json:"Platform"`
	PlatformVersion string `json:"PlatformVersion"`
	CPU             string `json:"CPU"`
	Arch            string `json:"Arch"`
	Virtualization  string `json:"Virtualization"`
	BootTime        uint64 `json:"BootTime"`
	CountryCode     string `json:"CountryCode"`
	Version         string `json:"Version"`
}

// Message WebSocket消息结构 (迁移自原main.go)
type Message struct {
	Type          string              `json:"type"`
	Password      string              `json:"password"`
	Data          ServerDetails       `json:"data,omitempty"`
	EncryptedData string              `json:"encrypted_data,omitempty"`
	Encrypted     bool                `json:"encrypted"`
	Timestamp     time.Time           `json:"timestamp"`
	// 服务数据
	SupervisorServices []SupervisorServiceData `json:"supervisor_services,omitempty"`
	SystemdServices    []SystemdServiceData    `json:"systemd_services,omitempty"`
	DockerServices     []DockerServiceData     `json:"docker_services,omitempty"`
}

// SupervisorServiceData Supervisor服务数据
type SupervisorServiceData struct {
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description"`
	PID         string `json:"pid"`
	Uptime      string `json:"uptime"`
}

// SystemdServiceData Systemd服务数据
type SystemdServiceData struct {
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description"`
}

// DockerServiceData Docker服务数据
type DockerServiceData struct {
	Name        string `json:"name"`
	Status      string `json:"status"`
	Image       string `json:"image"`
	Ports       string `json:"ports"`
	CreatedTime string `json:"created_time"`
}

// =============================================================================
// 转换函数
// =============================================================================

// ToServerStatus 将ServerDetails转换为ServerStatus
func (sd *ServerDetails) ToServerStatus() *ServerStatus {
	return &ServerStatus{
		ID:                sd.ID,
		Name:              sd.Name,
		Tag:               sd.Tag,
		LastActive:        sd.LastActive,
		IPv4:              sd.IPv4,
		IPv6:              sd.IPv6,
		ValidIP:           sd.ValidIP,
		StatusCPU:         sd.Status.CPU,
		StatusMemUsed:     sd.Status.MemUsed,
		StatusMemTotal:    sd.Status.MemTotal,
		StatusDiskUsed:    sd.Status.DiskUsed,
		StatusDiskTotal:   sd.Status.DiskTotal,
		StatusNetInSpeed:  sd.Status.NetInSpeed,
		StatusNetOutSpeed: sd.Status.NetOutSpeed,
		StatusUptime:      sd.Status.Uptime,
		StatusLoad1:       sd.Status.Load1,
		StatusLoad5:       sd.Status.Load5,
		StatusLoad15:      sd.Status.Load15,
	}
}

// ToServerInfo 将ServerDetails转换为ServerInfo
func (sd *ServerDetails) ToServerInfo() *ServerInfo {
	return &ServerInfo{
		ID:                  sd.ID,
		HostPlatform:        sd.Host.Platform,
		HostPlatformVersion: sd.Host.PlatformVersion,
		HostCPU:             sd.Host.CPU,
		HostArch:            sd.Host.Arch,
		HostVirtualization:  sd.Host.Virtualization,
		HostBootTime:        sd.Host.BootTime,
		HostCountryCode:     sd.Host.CountryCode,
		HostVersion:         sd.Host.Version,
	}
}

// ToServerStatusHistory 将ServerDetails转换为ServerStatusHistory
func (sd *ServerDetails) ToServerStatusHistory() *ServerStatusHistory {
	return &ServerStatusHistory{
		ServerID:          sd.ID,
		Timestamp:         time.Now(),
		StatusCPU:         sd.Status.CPU,
		StatusMemUsed:     sd.Status.MemUsed,
		StatusMemTotal:    sd.Status.MemTotal,
		StatusDiskUsed:    sd.Status.DiskUsed,
		StatusDiskTotal:   sd.Status.DiskTotal,
		StatusNetInSpeed:  sd.Status.NetInSpeed,
		StatusNetOutSpeed: sd.Status.NetOutSpeed,
		StatusUptime:      sd.Status.Uptime,
		StatusLoad1:       sd.Status.Load1,
		StatusLoad5:       sd.Status.Load5,
		StatusLoad15:      sd.Status.Load15,
	}
}

// ToStatusInfo 将ServerStatus转换为StatusInfo
func (ss *ServerStatus) ToStatusInfo() *StatusInfo {
	return &StatusInfo{
		CPU:         ss.StatusCPU,
		MemUsed:     ss.StatusMemUsed,
		MemTotal:    ss.StatusMemTotal,
		DiskUsed:    ss.StatusDiskUsed,
		DiskTotal:   ss.StatusDiskTotal,
		NetInSpeed:  ss.StatusNetInSpeed,
		NetOutSpeed: ss.StatusNetOutSpeed,
		Uptime:      ss.StatusUptime,
		Load1:       ss.StatusLoad1,
		Load5:       ss.StatusLoad5,
		Load15:      ss.StatusLoad15,
	}
}
