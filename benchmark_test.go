package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
)

// 测试数据结构
type ServerInfo struct {
	ID     int     `json:"id"`
	Name   string  `json:"name"`
	Status string  `json:"status"`
	CPU    float64 `json:"cpu"`
	Memory float64 `json:"memory"`
}

var testServerInfo = ServerInfo{
	ID:     1,
	Name:   "Test-Server",
	Status: "online",
	CPU:    45.2,
	Memory: 68.5,
}

// Fiber框架性能测试
func BenchmarkFiberHTTP(b *testing.B) {
	app := fiber.New()
	
	app.Get("/api/servers", func(c *fiber.Ctx) error {
		return c.JSON([]ServerInfo{testServerInfo})
	})
	
	app.Post("/api/servers", func(c *fiber.Ctx) error {
		var server ServerInfo
		if err := c.BodyParser(&server); err != nil {
			return c.Status(400).JSON(fiber.Map{"error": err.Error()})
		}
		return c.JSON(server)
	})

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// GET请求测试
			req := httptest.NewRequest("GET", "/api/servers", nil)
			resp, _ := app.Test(req)
			resp.Body.Close()
			
			// POST请求测试
			jsonData, _ := json.Marshal(testServerInfo)
			req = httptest.NewRequest("POST", "/api/servers", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			resp, _ = app.Test(req)
			resp.Body.Close()
		}
	})
}

// Gin框架性能测试
func BenchmarkGinHTTP(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	
	router.GET("/api/servers", func(c *gin.Context) {
		c.JSON(200, []ServerInfo{testServerInfo})
	})
	
	router.POST("/api/servers", func(c *gin.Context) {
		var server ServerInfo
		if err := c.ShouldBindJSON(&server); err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		c.JSON(200, server)
	})

	server := httptest.NewServer(router)
	defer server.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		client := &http.Client{}
		for pb.Next() {
			// GET请求测试
			resp, _ := client.Get(server.URL + "/api/servers")
			if resp != nil {
				resp.Body.Close()
			}
			
			// POST请求测试
			jsonData, _ := json.Marshal(testServerInfo)
			resp, _ = client.Post(server.URL+"/api/servers", "application/json", bytes.NewBuffer(jsonData))
			if resp != nil {
				resp.Body.Close()
			}
		}
	})
}

// Echo框架性能测试
func BenchmarkEchoHTTP(b *testing.B) {
	e := echo.New()
	
	e.GET("/api/servers", func(c echo.Context) error {
		return c.JSON(200, []ServerInfo{testServerInfo})
	})
	
	e.POST("/api/servers", func(c echo.Context) error {
		var server ServerInfo
		if err := c.Bind(&server); err != nil {
			return c.JSON(400, map[string]string{"error": err.Error()})
		}
		return c.JSON(200, server)
	})

	server := httptest.NewServer(e)
	defer server.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		client := &http.Client{}
		for pb.Next() {
			// GET请求测试
			resp, _ := client.Get(server.URL + "/api/servers")
			if resp != nil {
				resp.Body.Close()
			}
			
			// POST请求测试
			jsonData, _ := json.Marshal(testServerInfo)
			resp, _ = client.Post(server.URL+"/api/servers", "application/json", bytes.NewBuffer(jsonData))
			if resp != nil {
				resp.Body.Close()
			}
		}
	})
}

// WebSocket连接性能测试
func BenchmarkFiberWebSocket(b *testing.B) {
	app := fiber.New()
	
	var connectionCount int64
	var mu sync.Mutex
	
	app.Get("/ws", websocket.New(func(c *websocket.Conn) {
		mu.Lock()
		connectionCount++
		mu.Unlock()
		
		defer func() {
			mu.Lock()
			connectionCount--
			mu.Unlock()
			c.Close()
		}()
		
		for {
			var msg map[string]interface{}
			if err := c.ReadJSON(&msg); err != nil {
				break
			}
			
			// 回显消息
			if err := c.WriteJSON(msg); err != nil {
				break
			}
		}
	}))

	server := httptest.NewServer(app.Handler())
	defer server.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 模拟WebSocket连接
			wsURL := "ws" + server.URL[4:] + "/ws"
			conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
			if err != nil {
				continue
			}
			
			// 发送测试消息
			testMsg := map[string]interface{}{
				"type": "test",
				"data": "benchmark",
			}
			
			conn.WriteJSON(testMsg)
			
			var response map[string]interface{}
			conn.ReadJSON(&response)
			
			conn.Close()
		}
	})
}

// JSON序列化性能测试
func BenchmarkJSONSerialization(b *testing.B) {
	servers := make([]ServerInfo, 100)
	for i := 0; i < 100; i++ {
		servers[i] = ServerInfo{
			ID:     i + 1,
			Name:   fmt.Sprintf("Server-%d", i+1),
			Status: "online",
			CPU:    float64(i%100) + 0.5,
			Memory: float64((i*2)%100) + 0.3,
		}
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			data, _ := json.Marshal(servers)
			
			var decoded []ServerInfo
			json.Unmarshal(data, &decoded)
		}
	})
}

// 内存分配测试
func BenchmarkMemoryAllocation(b *testing.B) {
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 模拟创建大量小对象
			servers := make([]ServerInfo, 10)
			for i := 0; i < 10; i++ {
				servers[i] = ServerInfo{
					ID:     i,
					Name:   "Test",
					Status: "online",
					CPU:    50.0,
					Memory: 60.0,
				}
			}
			_ = servers
		}
	})
}

// 并发连接测试
func BenchmarkConcurrentConnections(b *testing.B) {
	app := fiber.New()
	
	app.Get("/api/test", func(c *fiber.Ctx) error {
		// 模拟一些处理时间
		time.Sleep(1 * time.Millisecond)
		return c.JSON(fiber.Map{"status": "ok"})
	})

	server := httptest.NewServer(app.Handler())
	defer server.Close()

	b.ResetTimer()
	
	// 测试不同并发级别
	concurrencyLevels := []int{1, 10, 50, 100, 500}
	
	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency-%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.RunParallel(func(pb *testing.PB) {
				client := &http.Client{
					Timeout: 5 * time.Second,
				}
				for pb.Next() {
					resp, err := client.Get(server.URL + "/api/test")
					if err == nil && resp != nil {
						io.Copy(io.Discard, resp.Body)
						resp.Body.Close()
					}
				}
			})
		})
	}
}

// 数据库操作模拟测试
func BenchmarkDatabaseOperations(b *testing.B) {
	// 模拟数据库操作
	simulateDBQuery := func() []ServerInfo {
		// 模拟数据库查询延迟
		time.Sleep(100 * time.Microsecond)
		
		return []ServerInfo{
			{ID: 1, Name: "Server-1", Status: "online", CPU: 45.2, Memory: 68.5},
			{ID: 2, Name: "Server-2", Status: "online", CPU: 32.1, Memory: 55.3},
			{ID: 3, Name: "Server-3", Status: "warning", CPU: 78.9, Memory: 82.1},
		}
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			servers := simulateDBQuery()
			
			// 模拟数据处理
			for _, server := range servers {
				if server.CPU > 80 || server.Memory > 90 {
					// 模拟告警处理
					_ = fmt.Sprintf("Alert: %s high usage", server.Name)
				}
			}
		}
	})
}

// 压力测试辅助函数
func runStressTest(name string, handler http.Handler, duration time.Duration, concurrency int) {
	fmt.Printf("\n=== %s 压力测试 ===\n", name)
	fmt.Printf("持续时间: %v, 并发数: %d\n", duration, concurrency)
	
	server := httptest.NewServer(handler)
	defer server.Close()
	
	var (
		totalRequests int64
		successCount  int64
		errorCount    int64
		mu           sync.Mutex
	)
	
	start := time.Now()
	done := make(chan bool)
	
	// 启动并发goroutine
	for i := 0; i < concurrency; i++ {
		go func() {
			client := &http.Client{Timeout: 5 * time.Second}
			for {
				select {
				case <-done:
					return
				default:
					resp, err := client.Get(server.URL + "/api/servers")
					
					mu.Lock()
					totalRequests++
					if err != nil || resp.StatusCode != 200 {
						errorCount++
					} else {
						successCount++
					}
					mu.Unlock()
					
					if resp != nil {
						resp.Body.Close()
					}
				}
			}
		}()
	}
	
	// 运行指定时间
	time.Sleep(duration)
	close(done)
	
	elapsed := time.Since(start)
	
	fmt.Printf("总请求数: %d\n", totalRequests)
	fmt.Printf("成功请求: %d\n", successCount)
	fmt.Printf("失败请求: %d\n", errorCount)
	fmt.Printf("QPS: %.2f\n", float64(totalRequests)/elapsed.Seconds())
	fmt.Printf("平均响应时间: %.2fms\n", elapsed.Seconds()*1000/float64(totalRequests))
	fmt.Printf("成功率: %.2f%%\n", float64(successCount)/float64(totalRequests)*100)
}

// 压力测试
func TestStressTest(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力测试")
	}
	
	// Fiber压力测试
	fiberApp := fiber.New()
	fiberApp.Get("/api/servers", func(c *fiber.Ctx) error {
		return c.JSON([]ServerInfo{testServerInfo})
	})
	
	runStressTest("Fiber", fiberApp.Handler(), 10*time.Second, 100)
	
	// Gin压力测试
	gin.SetMode(gin.ReleaseMode)
	ginRouter := gin.New()
	ginRouter.GET("/api/servers", func(c *gin.Context) {
		c.JSON(200, []ServerInfo{testServerInfo})
	})
	
	runStressTest("Gin", ginRouter, 10*time.Second, 100)
}

// 基准测试结果分析
func TestBenchmarkAnalysis(t *testing.T) {
	fmt.Println("\n=== 性能测试建议 ===")
	fmt.Println("1. 运行基准测试: go test -bench=. -benchmem")
	fmt.Println("2. 生成CPU性能分析: go test -bench=. -cpuprofile=cpu.prof")
	fmt.Println("3. 生成内存分析: go test -bench=. -memprofile=mem.prof")
	fmt.Println("4. 查看性能分析: go tool pprof cpu.prof")
	fmt.Println("5. 运行压力测试: go test -run=TestStressTest")
	fmt.Println("\n预期结果:")
	fmt.Println("- Fiber应该比Gin快2-3倍")
	fmt.Println("- WebSocket连接应该能处理1000+并发")
	fmt.Println("- JSON序列化应该在微秒级别")
	fmt.Println("- 内存分配应该尽可能少")
}
