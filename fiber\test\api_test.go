package test

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"server-monitor-fiber/internal/handlers"
)

// setupTestApp 创建测试用的Fiber应用
func setupTestApp() *fiber.App {
	app := fiber.New()
	
	// 设置路由
	handlers.SetupRoutes(app)
	
	return app
}

// TestHealthCheck 测试健康检查端点
func TestHealthCheck(t *testing.T) {
	app := setupTestApp()

	req := httptest.NewRequest("GET", "/api/health", nil)
	resp, err := app.Test(req)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	assert.NoError(t, err)
	assert.True(t, result["success"].(bool))
	assert.Equal(t, "服务器运行正常", result["message"])
}

// TestLogin 测试登录功能
func TestLogin(t *testing.T) {
	app := setupTestApp()

	// 测试成功登录
	t.Run("成功登录", func(t *testing.T) {
		loginData := map[string]string{
			"username": "xctcc",
			"password": "960423Wc@",
		}
		
		jsonData, _ := json.Marshal(loginData)
		req := httptest.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		resp, err := app.Test(req)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		assert.NoError(t, err)

		var result map[string]interface{}
		err = json.Unmarshal(body, &result)
		assert.NoError(t, err)
		assert.True(t, result["success"].(bool))
		assert.Equal(t, "登录成功", result["message"])
		
		// 检查是否有用户信息
		user := result["user"].(map[string]interface{})
		assert.Equal(t, "xctcc", user["username"])
		assert.Equal(t, "admin", user["role"])
	})

	// 测试登录失败
	t.Run("登录失败", func(t *testing.T) {
		loginData := map[string]string{
			"username": "wrong",
			"password": "wrong",
		}
		
		jsonData, _ := json.Marshal(loginData)
		req := httptest.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		resp, err := app.Test(req)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		assert.NoError(t, err)

		var result map[string]interface{}
		err = json.Unmarshal(body, &result)
		assert.NoError(t, err)
		assert.Equal(t, "用户名或密码错误", result["error"])
	})
}

// TestLogout 测试登出功能
func TestLogout(t *testing.T) {
	app := setupTestApp()

	req := httptest.NewRequest("POST", "/api/auth/logout", nil)
	resp, err := app.Test(req)

	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	assert.NoError(t, err)
	assert.True(t, result["success"].(bool))
	assert.Equal(t, "登出成功", result["message"])
}

// TestGetServers 测试获取服务器列表
func TestGetServers(t *testing.T) {
	app := setupTestApp()

	// 测试未认证访问
	t.Run("未认证访问", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/servers", nil)
		resp, err := app.Test(req)

		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	// 测试已认证访问
	t.Run("已认证访问", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/servers", nil)
		req.AddCookie(&http.Cookie{
			Name:  "auth_token",
			Value: "authenticated_user_token",
		})
		
		resp, err := app.Test(req)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		assert.NoError(t, err)

		var servers []map[string]interface{}
		err = json.Unmarshal(body, &servers)
		assert.NoError(t, err)
		assert.Len(t, servers, 12) // 应该返回12个服务器占位符
		
		// 检查第一个服务器的结构
		server := servers[0]
		assert.Equal(t, float64(1), server["id"])
		assert.Equal(t, "服务器 1", server["name"])
		assert.Equal(t, "offline", server["status"])
		assert.True(t, server["isPlaceholder"].(bool))
	})
}

// TestGetServerByID 测试获取指定服务器信息
func TestGetServerByID(t *testing.T) {
	app := setupTestApp()

	req := httptest.NewRequest("GET", "/api/servers/1", nil)
	req.AddCookie(&http.Cookie{
		Name:  "auth_token",
		Value: "authenticated_user_token",
	})
	
	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var server map[string]interface{}
	err = json.Unmarshal(body, &server)
	assert.NoError(t, err)
	assert.Equal(t, float64(1), server["id"])
	assert.Equal(t, "服务器 1", server["name"])
	assert.Equal(t, "online", server["status"])
}

// TestGetSystemStats 测试获取系统统计信息
func TestGetSystemStats(t *testing.T) {
	app := setupTestApp()

	req := httptest.NewRequest("GET", "/api/system/stats?serverId=1", nil)
	req.AddCookie(&http.Cookie{
		Name:  "auth_token",
		Value: "authenticated_user_token",
	})
	
	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var stats map[string]interface{}
	err = json.Unmarshal(body, &stats)
	assert.NoError(t, err)
	assert.Equal(t, 45.2, stats["CPU"])
	assert.Equal(t, 50.0, stats["memory"])
	assert.NotNil(t, stats["MemUsed"])
	assert.NotNil(t, stats["MemTotal"])
}

// TestGetServicesList 测试获取服务列表
func TestGetServicesList(t *testing.T) {
	app := setupTestApp()

	req := httptest.NewRequest("GET", "/api/services/list?serverId=1", nil)
	req.AddCookie(&http.Cookie{
		Name:  "auth_token",
		Value: "authenticated_user_token",
	})
	
	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var services []map[string]interface{}
	err = json.Unmarshal(body, &services)
	assert.NoError(t, err)
	assert.Greater(t, len(services), 0) // 应该有服务返回
	
	// 检查服务结构
	if len(services) > 0 {
		service := services[0]
		assert.NotEmpty(t, service["name"])
		assert.NotEmpty(t, service["status"])
		assert.NotEmpty(t, service["type"])
	}
}

// TestServiceAction 测试服务操作
func TestServiceAction(t *testing.T) {
	app := setupTestApp()

	serviceReq := map[string]interface{}{
		"serverId":    1,
		"serviceName": "nginx",
		"serviceType": "systemd",
	}
	
	jsonData, _ := json.Marshal(serviceReq)
	req := httptest.NewRequest("POST", "/api/services/systemd/start", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.AddCookie(&http.Cookie{
		Name:  "auth_token",
		Value: "authenticated_user_token",
	})
	
	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	assert.NoError(t, err)
	assert.True(t, result["success"].(bool))
	assert.Contains(t, result["message"], "nginx")
}

// TestGetServiceLogs 测试获取服务日志
func TestGetServiceLogs(t *testing.T) {
	app := setupTestApp()

	req := httptest.NewRequest("GET", "/api/services/logs?serverId=1&serviceName=nginx&serviceType=systemd", nil)
	req.AddCookie(&http.Cookie{
		Name:  "auth_token",
		Value: "authenticated_user_token",
	})
	
	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	logContent := string(body)
	assert.Contains(t, logContent, "nginx")
	assert.Contains(t, logContent, "systemd")
	assert.Contains(t, logContent, "INFO")
}

// BenchmarkHealthCheck 健康检查性能测试
func BenchmarkHealthCheck(b *testing.B) {
	app := setupTestApp()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", "/api/health", nil)
		resp, _ := app.Test(req)
		resp.Body.Close()
	}
}

// BenchmarkLogin 登录性能测试
func BenchmarkLogin(b *testing.B) {
	app := setupTestApp()
	
	loginData := map[string]string{
		"username": "xctcc",
		"password": "960423Wc@",
	}
	jsonData, _ := json.Marshal(loginData)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		resp, _ := app.Test(req)
		resp.Body.Close()
	}
}
