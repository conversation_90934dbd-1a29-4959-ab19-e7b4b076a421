package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/xctcc/hertz_project/pkg/dal"
	"github.com/xctcc/hertz_project/pkg/model"
)

// 全局变量，将在 Fiber 项目的 main.go 中初始化
var (
	ServerConfigGlobal *ServerConfig
	serverStatusRepo   *dal.ServerStatusRepository
	serverInfoRepo     *dal.ServerInfoRepository
)

// =============================================================================
// 数据结构定义 (与前端兼容)
// =============================================================================

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Success bool     `json:"success"`
	Message string   `json:"message"`
	User    UserInfo `json:"user,omitempty"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Details string `json:"details,omitempty"`
}

// ServerInfo 服务器信息 (与Vue3前端兼容)
type ServerInfo struct {
	ID             int     `json:"id"`
	Name           string  `json:"name"`
	IP             string  `json:"ip"`
	Hostname       string  `json:"hostname"`
	OS             string  `json:"os"`
	Status         string  `json:"status"` // 在线, 离线, 警告, 危险
	CPU            float64 `json:"cpu"`
	Memory         float64 `json:"memory"`
	NetInSpeed     uint64  `json:"NetInSpeed"`
	NetOutSpeed    uint64  `json:"NetOutSpeed"`
	NetInTransfer  uint64  `json:"NetInTransfer"`
	NetOutTransfer uint64  `json:"NetOutTransfer"`
	Uptime         string  `json:"uptime"`
	LastActive     int64   `json:"last_active"`
	IsPlaceholder  bool    `json:"isPlaceholder,omitempty"`
}

// SystemStats 系统统计信息
type SystemStats struct {
	CPU            float64   `json:"CPU"`
	MemUsed        uint64    `json:"MemUsed"`
	MemTotal       uint64    `json:"MemTotal"`
	Memory         float64   `json:"memory"`
	DiskUsed       uint64    `json:"DiskUsed"`
	DiskTotal      uint64    `json:"DiskTotal"`
	Disk           float64   `json:"disk"`
	NetInSpeed     uint64    `json:"NetInSpeed"`
	NetOutSpeed    uint64    `json:"NetOutSpeed"`
	NetInTransfer  uint64    `json:"NetInTransfer"`
	NetOutTransfer uint64    `json:"NetOutTransfer"`
	Uptime         uint64    `json:"Uptime"`
	LoadAvg        []float64 `json:"LoadAvg,omitempty"`
	Processes      int       `json:"Processes,omitempty"`
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	Name        string `json:"name"`
	Status      string `json:"status"` // 运行中, 已停止, 失败, 活跃, 非活跃, 错误
	Description string `json:"description,omitempty"`
	PID         string `json:"pid,omitempty"`
	CPU         string `json:"cpu,omitempty"`
	Memory      string `json:"memory,omitempty"`
	Uptime      string `json:"uptime,omitempty"`
	Type        string `json:"type"` // supervisor, systemd, docker
}

// ServiceRequest 服务操作请求
type ServiceRequest struct {
	ServerID    int    `json:"serverId"`
	ServiceName string `json:"serviceName"`
	ServiceType string `json:"serviceType"`      // supervisor, systemd, docker
	Action      string `json:"action,omitempty"` // 启动, 停止, 重启
}

// ServiceResponse 服务操作响应
type ServiceResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ServerConfig 结构体，从 main.go 复制过来
type ServerConfig struct {
	Listen        string `json:"listen"`
	Port          string `json:"port"`
	LoginUsername string `json:"login_username"`
	LoginPassword string `json:"login_password"`
	Servers       []struct {
		ID      int    `json:"mid"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	SecureCookie bool   `json:"secure_cookie"` // 用于 Cookie 安全的新字段
	Password     string `json:"password"`
}

// calculateServerStatus 根据CPU和内存使用率计算服务器状态 (从 main.go 复制过来)
func calculateServerStatus(cpuUsage, memoryUsage float64) string {
	if cpuUsage > 90 || memoryUsage > 90 {
		return "critical" // 危险
	} else if cpuUsage > 70 || memoryUsage > 70 {
		return "warning" // 警告
	} else {
		return "normal" // 正常
	}
}

// formatUptime 格式化秒数为人类可读的运行时间 (从 main.go 复制过来)
func formatUptime(seconds int64) string {
	if seconds < 0 {
		return "--"
	}
	days := seconds / (60 * 60 * 24)
	hours := (seconds % (60 * 60 * 24)) / (60 * 60)
	minutes := (seconds % (60 * 60)) / 60
	secs := seconds % 60

	parts := []string{}
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d 天", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d 小时", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d 分钟", minutes))
	}
	if secs > 0 || len(parts) == 0 { // 如果没有更大的单位，或者只有0秒，则始终显示秒
		parts = append(parts, fmt.Sprintf("%d 秒", secs))
	}
	return strings.Join(parts, ", ")
}

// convertToServerDetailsForFrontend 将后端ServerDetails转换为前端期望的FrontendServerDetails (从 main.go 复制过来)
func convertToServerDetailsForFrontend(server model.ServerDetails, host model.ServerInfo, status model.StatusInfo) ServerInfo {
	frontendDetails := ServerInfo{
		ID:             server.ID,
		Name:           server.Name,
		IP:             server.IPv4,
		OS:             host.HostPlatform,
		CPU:            status.CPU,
		NetInSpeed:     status.NetInSpeed,
		NetOutSpeed:    status.NetOutSpeed,
		NetInTransfer:  status.NetInTransfer,
		NetOutTransfer: status.NetOutTransfer,
	}

	// Hostname 可以使用 Platform 或 PlatformVersion，或更复杂的逻辑
	frontendDetails.Hostname = host.HostPlatform

	// 计算 Memory 使用率
	if status.MemTotal > 0 {
		frontendDetails.Memory = (float64(status.MemUsed) / float64(status.MemTotal)) * 100
	}

	// 格式化 Uptime
	frontendDetails.Uptime = formatUptime(int64(status.Uptime))

	// 计算 Status
	frontendDetails.Status = calculateServerStatus(status.CPU, frontendDetails.Memory)

	return frontendDetails
}

// =============================================================================
// 认证相关处理器
// =============================================================================

// LoginHandler 用户登录
// @Summary 用户登录
// @Description 用户登录获取认证Cookie
// @Tags 认证
// @Accept json
// @Produce json
// @Param credentials body LoginRequest true "登录凭据"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Router /api/auth/login [post]
func LoginHandler(c *fiber.Ctx) error {
	var req LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "无效的请求格式",
			Code:  fiber.StatusBadRequest,
		})
	}

	// 验证用户凭据（这里使用硬编码，实际应该从数据库验证）
	if req.Username == "xctcc" && req.Password == "960423Wc@" {
		// 设置httpOnly Cookie
		c.Cookie(&fiber.Cookie{
			Name:     "auth_token",
			Value:    "authenticated_user_token", // 实际应该是JWT或session ID
			Expires:  time.Now().Add(24 * time.Hour),
			HTTPOnly: true,
			Secure:   false, // 开发环境设为false，生产环境应为true
			SameSite: "Lax",
		})

		return c.JSON(LoginResponse{
			Success: true,
			Message: "登录成功",
			User: UserInfo{
				ID:       1,
				Username: req.Username,
				Role:     "admin",
			},
		})
	}

	return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
		Error: "用户名或密码错误",
		Code:  fiber.StatusUnauthorized,
	})
}

// LogoutHandler 用户登出
// @Summary 用户登出
// @Description 清除认证Cookie
// @Tags 认证
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "登出成功"
// @Router /api/auth/logout [post]
func LogoutHandler(c *fiber.Ctx) error {
	// 清除Cookie
	c.Cookie(&fiber.Cookie{
		Name:     "auth_token",
		Value:    "",
		Expires:  time.Now().Add(-time.Hour),
		HTTPOnly: true,
	})

	return c.JSON(fiber.Map{
		"success": true,
		"message": "登出成功",
	})
}

// =============================================================================
// 服务器监控相关处理器
// =============================================================================

// GetServersHandler 获取服务器列表
// @Summary 获取服务器列表
// @Description 获取所有监控服务器的状态信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security CookieAuth
// @Success 200 {array} ServerInfo "服务器列表"
// @Failure 401 {object} ErrorResponse "未授权"
// @Router /api/servers [get]
func GetServersHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  fiber.StatusUnauthorized,
		})
	}

	var responseServers []ServerInfo // 已从 FrontendServerDetails 更改为 ServerInfo

	// 从配置中收集所有服务器ID
	var serverIDs []int
	if ServerConfigGlobal == nil {
		log.Printf("ServerConfigGlobal 为空。请确保已初始化。")
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "服务器配置未初始化",
			Code:  fiber.StatusInternalServerError,
		})
	}
	for _, s := range ServerConfigGlobal.Servers {
		serverIDs = append(serverIDs, s.ID)
	}

	// 单次查询获取所有 ServerStatus 记录
	allServerStatuses, err := serverStatusRepo.GetAllServerStatuses(serverIDs)
	if err != nil {
		log.Printf("从数据库获取所有 ServerStatus 错误: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "无法检索服务器状态。",
			Code:  fiber.StatusInternalServerError,
		})
	}

	// 创建一个 map 以便快速查找
	serverStatusMap := make(map[int]model.ServerStatus)
	for _, ss := range allServerStatuses {
		serverStatusMap[ss.ID] = ss
	}

	// 单次查询获取所有 ServerInfo 记录
	allServerInfos, err := serverInfoRepo.GetAllServerInfos(serverIDs)
	if err != nil {
		log.Printf("从数据库获取所有 ServerInfo 错误: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "无法检索服务器信息。",
			Code:  fiber.StatusInternalServerError,
		})
	}

	// 创建一个 map 以便快速查找
	serverInfoMap := make(map[int]model.ServerInfo)
	for _, si := range allServerInfos {
		serverInfoMap[si.ID] = si
	}

	// 使用获取到的数据处理配置的服务器
	for _, s := range ServerConfigGlobal.Servers {
		serverStatus, statusFound := serverStatusMap[s.ID]
		serverInfo, infoFound := serverInfoMap[s.ID]

		if !statusFound || !infoFound {
			// 如果状态或信息未找到，则认为服务器离线或未知
			status := "offline"            // 离线
			if !statusFound && infoFound { // 如果信息找到但状态未找到，则可能是新服务器或尚未报告统计信息
				status = "no data" // 无数据
			} else if statusFound && !infoFound { // 如果状态找到但信息未找到（在正确数据下不应发生，但为了健壮性）
				status = "unknown info" // 未知信息
			}

			responseServers = append(responseServers, ServerInfo{ // 更改为 ServerInfo
				ID:             s.ID,
				Name:           s.Name,
				IP:             "",
				Hostname:       "",
				OS:             "",
				Status:         status,
				CPU:            0,
				Memory:         0,
				NetInSpeed:     0,
				NetOutSpeed:    0,
				NetInTransfer:  0,
				NetOutTransfer: 0,
				Uptime:         "0 秒", // 更改为中文
				IsPlaceholder:  true,  // 如果数据缺失则标记为占位符
			})
			log.Printf("服务器 ID %d (%s) 缺少数据 (状态找到: %t, 信息找到: %t)，返回 %s 状态。", s.ID, s.Name, statusFound, infoFound, status)
			continue
		}

		// 组合数据并转换为前端期望的结构
		serverDetail := convertToServerDetailsForFrontend(
			model.ServerDetails{
				ID:         serverStatus.ID,
				Name:       serverStatus.Name,
				Tag:        serverStatus.Tag,
				LastActive: serverStatus.LastActive,
				IPv4:       serverStatus.IPv4,
				IPv6:       serverStatus.IPv6,
				ValidIP:    serverStatus.ValidIP,
			},
			serverInfo, // 传递 serverInfo (HostInfo)
			model.StatusInfo{ // 传递 StatusInfo
				CPU:            serverStatus.StatusCPU,
				MemUsed:        serverStatus.StatusMemUsed,
				MemTotal:       serverStatus.StatusMemTotal,
				DiskUsed:       serverStatus.StatusDiskUsed,
				DiskTotal:      serverStatus.StatusDiskTotal,
				NetInSpeed:     serverStatus.StatusNetInSpeed,
				NetOutSpeed:    serverStatus.StatusNetOutSpeed,
				Uptime:         serverStatus.StatusUptime,
				Load1:          serverStatus.StatusLoad1,
				Load5:          serverStatus.StatusLoad5,
				Load15:         serverStatus.StatusLoad15,
				NetInTransfer:  serverStatus.StatusNetInTransfer,
				NetOutTransfer: serverStatus.StatusNetOutTransfer,
			},
		)
		responseServers = append(responseServers, serverDetail)
	}

	return c.JSON(responseServers)
}

// GetServerByIDHandler 获取指定服务器信息
// @Summary 获取指定服务器信息
// @Description 根据服务器ID获取详细信息
// @Tags 服务器
// @Accept json
// @Produce json
// @Security CookieAuth
// @Param id path int true "服务器ID"
// @Success 200 {object} ServerInfo "服务器信息"
// @Failure 404 {object} ErrorResponse "服务器不存在"
// @Router /api/servers/{id} [get]
func GetServerByIDHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  fiber.StatusUnauthorized,
		})
	}

	serverIDStr := c.Params("id")
	serverID, err := strconv.Atoi(serverIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "无效的服务器ID",
			Code:  fiber.StatusBadRequest,
		})
	}

	// 这里应该根据ID从数据库获取服务器信息
	// 暂时返回模拟数据
	return c.JSON(ServerInfo{
		ID:       serverID,
		Name:     fmt.Sprintf("服务器 %d", serverID),
		IP:       "*************",
		Hostname: fmt.Sprintf("server-%d", serverID),
		OS:       "Ubuntu 20.04",
		Status:   "online", // 在线
		CPU:      45.2,
		Memory:   68.5,
		Uptime:   "15 天, 3 小时, 45 分钟, 22 秒", // 更改为中文
	})
}

// GetSystemStatsHandler 获取系统统计信息
// @Summary 获取系统统计信息
// @Description 获取指定服务器的系统统计信息
// @Tags 系统
// @Accept json
// @Produce json
// @Security CookieAuth
// @Param serverId query int true "服务器ID"
// @Success 200 {object} SystemStats "系统统计信息"
// @Router /api/system/stats [get]
func GetSystemStatsHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  fiber.StatusUnauthorized,
		})
	}

	// 返回模拟的系统统计数据
	stats := SystemStats{
		CPU:            45.2,
		MemUsed:        8589934592,  // 8GB
		MemTotal:       17179869184, // 16GB
		Memory:         50.0,
		DiskUsed:       536870912000,  // 500GB
		DiskTotal:      1073741824000, // 1TB
		Disk:           50.0,
		NetInSpeed:     1048576,       // 1MB/s
		NetOutSpeed:    524288,        // 512KB/s
		NetInTransfer:  1073741824000, // 1TB
		NetOutTransfer: 536870912000,  // 500GB
		Uptime:         1296000,       // 15 天
	}

	return c.JSON(stats)
}

// =============================================================================
// 服务管理相关处理器
// =============================================================================

// GetServicesListHandler 获取服务列表
// @Summary 获取服务列表
// @Description 获取指定服务器的所有服务信息
// @Tags 服务
// @Accept json
// @Produce json
// @Security CookieAuth
// @Param serverId query int true "服务器ID"
// @Param serviceType query string false "服务类型过滤" Enums(supervisor, systemd, docker)
// @Success 200 {array} ServiceInfo "服务列表"
// @Router /api/services/list [get]
func GetServicesListHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  fiber.StatusUnauthorized,
		})
	}

	// 返回模拟的服务列表
	services := []ServiceInfo{
		{
			Name:        "nginx",
			Status:      "running",        // 运行中
			Description: "Nginx HTTP 服务器", // 更改为中文
			PID:         "1234",
			CPU:         "2.5%",
			Memory:      "128MB",
			Uptime:      "15 天", // 更改为中文
			Type:        "systemd",
		},
		{
			Name:        "mysql",
			Status:      "running",      // 运行中
			Description: "MySQL 数据库服务器", // 更改为中文
			PID:         "5678",
			CPU:         "5.2%",
			Memory:      "512MB",
			Uptime:      "15 天", // 更改为中文
			Type:        "systemd",
		},
	}

	return c.JSON(services)
}

// ServiceActionHandler 服务操作处理器
// @Summary 控制服务
// @Description 对指定服务器的服务执行启动、停止或重启操作
// @Tags 服务
// @Accept json
// @Produce json
// @Security CookieAuth
// @Param type path string true "服务类型" Enums(supervisor, systemd, docker)
// @Param action path string true "操作类型" Enums(start, stop, restart)
// @Param request body ServiceRequest true "服务控制请求"
// @Success 200 {object} ServiceResponse "操作成功"
// @Router /api/services/{type}/{action} [post]
func ServiceActionHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  fiber.StatusUnauthorized,
		})
	}

	serviceType := c.Params("type")
	action := c.Params("action")

	var req ServiceRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "无效的请求格式",
			Code:  fiber.StatusBadRequest,
		})
	}

	// 这里应该实现实际的服务控制逻辑
	log.Printf("服务操作: 类型=%s, 动作=%s, 服务=%s, 服务器ID=%d",
		serviceType, action, req.ServiceName, req.ServerID)

	return c.JSON(ServiceResponse{
		Success: true,
		Message: fmt.Sprintf("成功%s服务 %s", action, req.ServiceName),
	})
}

// GetServiceLogsHandler 获取服务日志
// @Summary 获取服务日志
// @Description 获取指定服务的日志信息
// @Tags 服务
// @Accept json
// @Produce plain
// @Security CookieAuth
// @Param serverId query int true "服务器ID"
// @Param serviceName query string true "服务名称"
// @Param serviceType query string true "服务类型" Enums(supervisor, systemd, docker)
// @Param lines query int false "日志行数" default(100)
// @Success 200 {string} string "服务日志内容"
// @Router /api/services/logs [get]
func GetServiceLogsHandler(c *fiber.Ctx) error {
	if !isAuthenticated(c) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "未授权访问",
			Code:  fiber.StatusUnauthorized,
		})
	}

	serviceName := c.Query("serviceName")
	serviceType := c.Query("serviceType")

	// 返回模拟的日志内容
	logContent := fmt.Sprintf(`[2023-12-01 10:30:00] 信息: %s 服务已启动
[2023-12-01 10:30:01] 信息: 服务类型: %s
[2023-12-01 10:30:02] 信息: 服务正常运行
[2023-12-01 10:30:03] 调试: 正在处理请求
[2023-12-01 10:30:04] 信息: 健康检查通过
`, serviceName, serviceType)

	return c.SendString(logContent)
}

// =============================================================================
// 健康检查处理器
// =============================================================================

// HealthCheckHandler 健康检查
// @Summary 健康检查
// @Description 检查服务器运行状态
// @Tags 系统
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "服务器运行正常"
// @Router /api/health [get]
func HealthCheckHandler(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"success":   true,
		"message":   "服务器运行正常",
		"timestamp": time.Now(),
		"version":   "2.0.0",
	})
}

// =============================================================================
// 辅助函数
// =============================================================================

// isAuthenticated 检查用户是否已认证
func isAuthenticated(c *fiber.Ctx) bool {
	// 检查Cookie中的认证信息
	authToken := c.Cookies("auth_token")
	return authToken == "authenticated_user_token"
}
