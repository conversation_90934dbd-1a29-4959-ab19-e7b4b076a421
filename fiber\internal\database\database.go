package database

import (
	"log"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	db *gorm.DB
)

// InitDatabase 初始化数据库连接 (迁移自原main.go)
func InitDatabase(dbPath string) error {
	var err error
	
	// 配置GORM日志
	gormLogger := logger.New(
		log.Default(),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Warn,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// 连接SQLite数据库
	db, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(10)
	// 设置最大打开连接数
	sqlDB.SetMaxOpenConns(100)
	// 设置连接的最大生存时间
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 自动迁移数据库表
	err = autoMigrate()
	if err != nil {
		return err
	}

	log.Printf("数据库初始化成功: %s", dbPath)
	return nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate() error {
	return db.AutoMigrate(
		&ServerStatus{},
		&ServerInfo{},
		&ServerStatusHistory{},
		&SupervisorService{},
		&SystemdService{},
		&DockerService{},
	)
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return db
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if db != nil {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// =============================================================================
// 仓库接口定义
// =============================================================================

// ServerStatusRepository 服务器状态仓库接口
type ServerStatusRepository interface {
	SaveServerStatus(status *ServerStatus) error
	GetServerStatus(id int) (*ServerStatus, error)
	GetAllServerStatuses(ids []int) ([]ServerStatus, error)
	CreateServerStatusHistory(history *ServerStatusHistory) error
	GetServerStatusHistory(serverID int, limit int) ([]ServerStatusHistory, error)
}

// ServerInfoRepository 服务器信息仓库接口
type ServerInfoRepository interface {
	UpdateServerInfo(info *ServerInfo) error
	GetServerInfo(id int) (*ServerInfo, error)
	GetAllServerInfos(ids []int) ([]ServerInfo, error)
}

// ServiceRepository 服务仓库接口
type ServiceRepository interface {
	ProcessServiceUpdates(serverID int, supervisorServices []SupervisorServiceData, systemdServices []SystemdServiceData, dockerServices []DockerServiceData)
	GetSupervisorServices(serverID int) ([]SupervisorService, error)
	GetSystemdServices(serverID int) ([]SystemdService, error)
	GetDockerServices(serverID int) ([]DockerService, error)
}

// =============================================================================
// 仓库实现
// =============================================================================

// serverStatusRepo 服务器状态仓库实现
type serverStatusRepo struct {
	db *gorm.DB
}

// NewServerStatusRepository 创建服务器状态仓库
func NewServerStatusRepository(database *gorm.DB) ServerStatusRepository {
	return &serverStatusRepo{db: database}
}

func (r *serverStatusRepo) SaveServerStatus(status *ServerStatus) error {
	return r.db.Save(status).Error
}

func (r *serverStatusRepo) GetServerStatus(id int) (*ServerStatus, error) {
	var status ServerStatus
	err := r.db.Where("id = ?", id).First(&status).Error
	if err != nil {
		return nil, err
	}
	return &status, nil
}

func (r *serverStatusRepo) GetAllServerStatuses(ids []int) ([]ServerStatus, error) {
	var statuses []ServerStatus
	err := r.db.Where("id IN ?", ids).Find(&statuses).Error
	return statuses, err
}

func (r *serverStatusRepo) CreateServerStatusHistory(history *ServerStatusHistory) error {
	return r.db.Create(history).Error
}

func (r *serverStatusRepo) GetServerStatusHistory(serverID int, limit int) ([]ServerStatusHistory, error) {
	var history []ServerStatusHistory
	err := r.db.Where("server_id = ?", serverID).
		Order("timestamp DESC").
		Limit(limit).
		Find(&history).Error
	return history, err
}

// serverInfoRepo 服务器信息仓库实现
type serverInfoRepo struct {
	db *gorm.DB
}

// NewServerInfoRepository 创建服务器信息仓库
func NewServerInfoRepository(database *gorm.DB) ServerInfoRepository {
	return &serverInfoRepo{db: database}
}

func (r *serverInfoRepo) UpdateServerInfo(info *ServerInfo) error {
	return r.db.Save(info).Error
}

func (r *serverInfoRepo) GetServerInfo(id int) (*ServerInfo, error) {
	var info ServerInfo
	err := r.db.Where("id = ?", id).First(&info).Error
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (r *serverInfoRepo) GetAllServerInfos(ids []int) ([]ServerInfo, error) {
	var infos []ServerInfo
	err := r.db.Where("id IN ?", ids).Find(&infos).Error
	return infos, err
}

// serviceRepo 服务仓库实现
type serviceRepo struct {
	db *gorm.DB
}

// NewServiceRepository 创建服务仓库
func NewServiceRepository(database *gorm.DB) ServiceRepository {
	return &serviceRepo{db: database}
}

func (r *serviceRepo) ProcessServiceUpdates(serverID int, supervisorServices []SupervisorServiceData, systemdServices []SystemdServiceData, dockerServices []DockerServiceData) {
	// 处理Supervisor服务
	if len(supervisorServices) > 0 {
		// 删除旧数据
		r.db.Where("server_id = ?", serverID).Delete(&SupervisorService{})
		
		// 插入新数据
		for _, svc := range supervisorServices {
			service := SupervisorService{
				ServerID:    serverID,
				Name:        svc.Name,
				Status:      svc.Status,
				Description: svc.Description,
				PID:         svc.PID,
				Uptime:      svc.Uptime,
				UpdatedAt:   time.Now(),
			}
			r.db.Create(&service)
		}
	}

	// 处理Systemd服务
	if len(systemdServices) > 0 {
		// 删除旧数据
		r.db.Where("server_id = ?", serverID).Delete(&SystemdService{})
		
		// 插入新数据
		for _, svc := range systemdServices {
			service := SystemdService{
				ServerID:    serverID,
				Name:        svc.Name,
				Status:      svc.Status,
				Description: svc.Description,
				UpdatedAt:   time.Now(),
			}
			r.db.Create(&service)
		}
	}

	// 处理Docker服务
	if len(dockerServices) > 0 {
		// 删除旧数据
		r.db.Where("server_id = ?", serverID).Delete(&DockerService{})
		
		// 插入新数据
		for _, svc := range dockerServices {
			service := DockerService{
				ServerID:    serverID,
				Name:        svc.Name,
				Status:      svc.Status,
				Image:       svc.Image,
				Ports:       svc.Ports,
				CreatedTime: svc.CreatedTime,
				UpdatedAt:   time.Now(),
			}
			r.db.Create(&service)
		}
	}
}

func (r *serviceRepo) GetSupervisorServices(serverID int) ([]SupervisorService, error) {
	var services []SupervisorService
	err := r.db.Where("server_id = ?", serverID).Find(&services).Error
	return services, err
}

func (r *serviceRepo) GetSystemdServices(serverID int) ([]SystemdService, error) {
	var services []SystemdService
	err := r.db.Where("server_id = ?", serverID).Find(&services).Error
	return services, err
}

func (r *serviceRepo) GetDockerServices(serverID int) ([]DockerService, error) {
	var services []DockerService
	err := r.db.Where("server_id = ?", serverID).Find(&services).Error
	return services, err
}
