package middleware

import (
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

// Setup 设置所有中间件
func Setup(app *fiber.App) {
	// 恢复中间件 - 捕获panic并返回500错误
	app.Use(recover.New(recover.Config{
		EnableStackTrace: true,
	}))

	// 日志中间件
	app.Use(logger.New(logger.Config{
		Format:     "[${time}] ${status} - ${method} ${path} (${latency}) ${ip}\n",
		TimeFormat: "2006-01-02 15:04:05",
	}))

	// CORS中间件 - 支持跨域请求
	app.Use(cors.New(cors.Config{
		AllowOrigins:     "*",
		AllowMethods:     "GET,POST,HEAD,PUT,DELETE,PATCH,OPTIONS",
		AllowHeaders:     "Origin, Content-Type, Accept, Authorization, Cookie",
		AllowCredentials: true, // 允许携带Cookie
		ExposeHeaders:    "Content-Length",
		MaxAge:           86400, // 24小时
	}))

	// 静态文件服务 - 服务web目录
	app.Static("/", "../web", fiber.Static{
		Compress:      true,
		ByteRange:     true,
		Browse:        false,
		Index:         "index.html",
		CacheDuration: 0, // 开发环境不缓存
	})

	// 自定义头部中间件
	app.Use(func(c *fiber.Ctx) error {
		// 设置安全头部
		c.Set("X-Content-Type-Options", "nosniff")
		c.Set("X-Frame-Options", "DENY")
		c.Set("X-XSS-Protection", "1; mode=block")

		// 开发环境允许所有来源
		if c.Get("Origin") != "" {
			c.Set("Access-Control-Allow-Origin", c.Get("Origin"))
		}

		return c.Next()
	})
}

// AuthMiddleware 认证中间件
func AuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 检查Cookie中的认证信息
		authToken := c.Cookies("auth_token")
		if authToken != "authenticated_user_token" {
			return c.Status(401).JSON(fiber.Map{
				"error": "未授权访问",
				"code":  401,
			})
		}
		return c.Next()
	}
}

// RateLimitMiddleware 速率限制中间件（可选）
func RateLimitMiddleware() fiber.Handler {
	// 这里可以实现速率限制逻辑
	return func(c *fiber.Ctx) error {
		// 简单的速率限制实现
		return c.Next()
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 生成或获取请求ID
		requestID := c.Get("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
			c.Set("X-Request-ID", requestID)
		}

		// 将请求ID存储在上下文中
		c.Locals("requestID", requestID)

		return c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的请求ID生成
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}
